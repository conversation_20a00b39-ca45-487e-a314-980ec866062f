// Settings API - Functions for application settings and theme settings
import { getDatabase } from '../database/database';
import { dbGet, dbAll, dbRun } from '../database/database-api';
import type Database from 'better-sqlite3';

// Define interfaces for clarity
interface Setting {
    id: number;
    key: string;
    value_json: string; // Store value as JSON string
    value?: any; // Parsed value
    category: string;
    created_at?: string; // Optional since not in DB schema
    updated_at: string;
}

interface Theme {
    id: number;
    theme_name: string;
    is_active: 0 | 1; // Use 0 or 1 for boolean representation in SQLite
    created_at: string;
    updated_at: string;
}



// Helper to parse setting value
const parseSettingValue = (setting: Setting | undefined): Setting | undefined => {
    if (setting && setting.value_json) {
        try {
            setting.value = JSON.parse(setting.value_json);
        } catch (e) {
            console.error(`Error parsing JSON for setting ${setting.key}:`, e);
            setting.value = null; // Assign null or keep original string on error?
        }
    }
    return setting;
};

// Get a setting by key
export const getSetting = async (key: string): Promise<Setting | undefined> => {
    try {
        const setting = dbGet<Setting>('SELECT * FROM settings WHERE key = ?', [key]);
        return parseSettingValue(setting);
    } catch (error: any) {
        console.error(`Error getting setting ${key}:`, error);
        throw new Error(`Failed to get setting ${key}: ${error.message}`);
    }
};

// Get settings by category
export const getSettingsByCategory = async (category: string): Promise<Setting[]> => {
    try {
        const settings = dbAll<Setting>('SELECT * FROM settings WHERE category = ?', [category]);
        return settings.map(s => parseSettingValue(s)!); // Use non-null assertion if parse always returns
    } catch (error: any) {
        console.error(`Error getting settings for category ${category}:`, error);
        throw new Error(`Failed to get settings for category ${category}: ${error.message}`);
    }
};

// Get all settings
export const getAllSettings = async (): Promise<Setting[]> => {
    try {
        const settings = dbAll<Setting>('SELECT * FROM settings');
        return settings.map(s => parseSettingValue(s)!);
    } catch (error: any) {
        console.error('Error getting all settings:', error);
        throw new Error(`Failed to get all settings: ${error.message}`);
    }
};

// Set a setting (or create if it doesn't exist)
export const setSetting = async (key: string, value: any, category: string = 'general'): Promise<Setting> => {
    try {
        const valueJson = JSON.stringify(value);

        // Check if setting exists
        const existing = dbGet<{ id: number }>('SELECT id FROM settings WHERE key = ?', [key]);

        let resultId: number;
        if (existing) {
            // Update existing setting
            dbRun(
                'UPDATE settings SET value_json = ?, category = ?, updated_at = CURRENT_TIMESTAMP WHERE key = ?',
                [valueJson, category, key]
            );
            resultId = existing.id;
        } else {
            // Create new setting
            const result = dbRun(
                'INSERT INTO settings (key, value_json, category, updated_at) VALUES (?, ?, ?, CURRENT_TIMESTAMP)',
                [key, valueJson, category]
            );
            resultId = result.lastInsertRowid;
        }
        // Fetch the created/updated setting by key to ensure we get it
        const updatedSetting = dbGet<Setting>('SELECT * FROM settings WHERE key = ?', [key]);
        if (!updatedSetting) {
            throw new Error('Failed to retrieve the setting after update/insert.');
        }
        return parseSettingValue(updatedSetting)!;

    } catch (error: any) {
        console.error(`Error setting ${key}:`, error);
        throw new Error(`Failed to set setting ${key}: ${error.message}`);
    }
};

// Delete a setting by key
export const deleteSetting = async (key: string): Promise<{ success: boolean; key: string }> => {
    try {
        const result = dbRun('DELETE FROM settings WHERE key = ?', [key]);
        return { success: result.changes > 0, key };
    } catch (error: any) {
        console.error(`Error deleting setting ${key}:`, error);
        throw new Error(`Failed to delete setting ${key}: ${error.message}`);
    }
};

// === Theme Settings ===

// Get active theme
export const getActiveTheme = async (): Promise<Theme | undefined> => {
    try {
        return dbGet<Theme>('SELECT * FROM theme_settings WHERE is_active = 1');
    } catch (error: any) {
        console.error('Error getting active theme:', error);
        throw new Error(`Failed to get active theme: ${error.message}`);
    }
};

// Get all themes
export const getAllThemes = async (): Promise<Theme[]> => {
    try {
        return dbAll<Theme>('SELECT * FROM theme_settings ORDER BY theme_name'); // Added ordering
    } catch (error: any) {
        console.error('Error getting all themes:', error);
        throw new Error(`Failed to get all themes: ${error.message}`);
    }
};

// Create a new theme
export const createTheme = async (themeName: string): Promise<Theme> => {
    if (!themeName || typeof themeName !== 'string' || themeName.trim() === '') {
        throw new Error('Theme name must be a non-empty string');
    }
    try {
        const result = dbRun(
            'INSERT INTO theme_settings (theme_name, is_active, created_at, updated_at) VALUES (?, 0, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)', // Added created_at
            [themeName.trim()]
        );
        // Fetch the created theme
        const newTheme = dbGet<Theme>('SELECT * FROM theme_settings WHERE id = ?', [result.lastInsertRowid]);
        if (!newTheme) {
            throw new Error('Failed to retrieve the theme after creation.');
        }
        return newTheme;
    } catch (error: any) {
        console.error('Error creating theme:', error);
        // Handle potential unique constraint errors
        if (error.message?.includes('UNIQUE constraint failed')) {
            throw new Error(`Theme with name "${themeName.trim()}" already exists.`);
        }
        throw new Error(`Failed to create theme: ${error.message}`);
    }
};

// Set active theme using a transaction
export const setActiveTheme = async (themeId: number): Promise<Theme> => {
    if (typeof themeId !== 'number') {
        throw new Error('Theme ID must be a number');
    }

    try {
        const db = getDatabase();

        // Use better-sqlite3 transaction pattern
        const transaction = db.transaction((id: number) => {
            // Set all themes to inactive
            db.prepare('UPDATE theme_settings SET is_active = 0').run();

            // Set the specified theme to active
            const updateResult = db.prepare(
                'UPDATE theme_settings SET is_active = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?'
            ).run(id);

            if (updateResult.changes === 0) {
                throw new Error(`Theme with ID ${id} not found.`);
            }

            return id;
        });

        // Execute the transaction
        const resultId = transaction(themeId);

        // Return the updated theme (fetch after transaction)
        const activeTheme = dbGet<Theme>('SELECT * FROM theme_settings WHERE id = ?', [resultId]);
        if (!activeTheme) {
            // This case should ideally not happen if changes > 0, but good for safety
            throw new Error('Failed to retrieve the active theme after update.');
        }
        return activeTheme;

    } catch (error: any) {
        console.error(`Error setting active theme ${themeId}:`, error);
        throw new Error(`Failed to set active theme ${themeId}: ${error.message}`);
    }
};

// Delete a theme
export const deleteTheme = async (themeId: number): Promise<{ success: boolean; id: number }> => {
    if (typeof themeId !== 'number') {
        throw new Error('Theme ID must be a number');
    }
    try {
        // Check if theme exists and is active
        const theme = dbGet<Theme>('SELECT is_active FROM theme_settings WHERE id = ?', [themeId]);

        if (!theme) {
             throw new Error(`Theme with ID ${themeId} not found.`);
        }
        if (theme.is_active) {
            throw new Error('Cannot delete the currently active theme');
        }

        const result = dbRun('DELETE FROM theme_settings WHERE id = ?', [themeId]);
        return { success: result.changes > 0, id: themeId };
    } catch (error: any) {
        console.error(`Error deleting theme ${themeId}:`, error);
        throw new Error(`Failed to delete theme ${themeId}: ${error.message}`);
    }
};

// Export API functions
export default {
    // General settings
    getSetting,
    getSettingsByCategory,
    getAllSettings,
    setSetting,
    deleteSetting,

    // Theme settings
    getActiveTheme,
    getAllThemes,
    createTheme,
    setActiveTheme,
    deleteTheme
};