# Phase 4: Timer API Rewrite

**AI Agent Instructions**: Use extended thinking for all decisions. Meticulously review this plan before execution. Always read and analyze files thoroughly before making any modifications. Follow this plan step-by-step without deviation.

## Overview
**File:** `electron/main/api/timer-api.ts` (HIGH, complex transaction logic)

**Complexity:** HIGH - Contains complex transaction logic and critical timer functionality

## Tasks

### 4.1 Imports/types
- Remove sqlite3 imports and type alias Database = sqlite3.Database.
- Import getDatabase and, optionally, local helpers similar to database-api.ts or reuse dbGet/dbAll/dbRun from that module if exported. Prefer reusing database-api helpers to centralize DB calls.

### 4.2 Replace local helpers dbGet/dbAll/dbRun
Use shared helpers from database-api.ts or reimplement as sync wrappers here:
- dbGet<T>(): stmt.get(...params)
- dbAll<T>(): stmt.all(...params)
- dbRun(): stmt.run(...params) returning { changes, lastInsertRowid }

### 4.3 Rewrite all callsites to drop async/await when not needed
Many functions can be synchronous now. However, maintain async function signatures to avoid API churn if the rest of the app expects Promises. Implementation can return Promise.resolve(value) for compatibility, or leave them as sync and update callers. Safer approach: keep async signatures but run sync logic inside try/catch and return values.

Recommended pattern:
- Keep exported API signatures as async to avoid widespread changes.
- Internally execute synchronous better-sqlite3 calls; simply return values.

### 4.4 Transaction-heavy function: completePomodoroInSession
Convert manual BEGIN/COMMIT with serialize() into db.transaction():
```typescript
const db = getDatabase();
const fn = db.transaction((sessionId: number, cycleId: number) => {
  const updateCycle = db.prepare(...).run(cycleId, sessionId);
  if (!updateCycle.changes) throw new Error(...);

  const cycleType = db.prepare('SELECT cycle_type FROM pomodoro_cycles WHERE id = ?').get(cycleId)?.cycle_type;
  if (cycleType === 'pomodoro') {
    db.prepare('UPDATE timer_sessions SET pomodoro_cycles_completed = pomodoro_cycles_completed + 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?').run(sessionId);
  }
  const completed = db.prepare('SELECT * FROM pomodoro_cycles WHERE id = ?').get(cycleId);
  if (!completed) throw new Error('Failed to retrieve completed cycle');
  return completed;
});
return fn(sessionId, cycleId);
```

### 4.5 Other functions
Functions to update:
- createUserSession, endUserSession, updateSession, startPomodoroInSession, getActiveCycleForSession, cancelActiveCycle, startTimerSession, endTimerSession, getTimerSession, deleteTimerSession, getTimerSessionsByDateRange, getTodayTimerSessions, getTimerStatsByDateRange, getTimerSettings, updateTimerSettings, resetTimerSettings, syncAllSessionPomodoroCounts

For each:
- Replace dbRun/dbGet/dbAll usage with synchronous equivalents.
- Where prior logic retried on "Database is still initializing", this is likely unnecessary now due to sync init and singleton connection. Remove retry code. If any race persists at app startup, ensure getDatabase() is invoked after initDatabase() is called early, or lazily init as in Phase 2.
- Preserve ISO timestamp usage and SQL for computing durations.
- Preserve existing validation, but streamline with sync operations.

### 4.6 Consistency and snapshot reads
With better-sqlite3, wrap multi-query read sequences requiring a consistent view using db.transaction().immediate() if needed (replacing prior "BEGIN IMMEDIATE TRANSACTION"). For simple reads, single statements suffice.

## Dependencies
- Phase 3 must be completed (database-api layer rewritten)

## Prerequisites
- database-api.ts fully migrated with shared helpers available
- Understanding of current timer-api.ts transaction patterns

## Deliverables
- timer-api.ts uses synchronous better-sqlite3 calls, with critical transactions converted to db.transaction and async signatures retained for compatibility.
- All complex transaction logic properly converted to better-sqlite3 patterns
- Database initialization retry logic removed
- All timer functionality preserved and working correctly
