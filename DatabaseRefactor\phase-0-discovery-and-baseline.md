# Phase 0: Discovery and Baseline

**AI Agent Instructions**: Use extended thinking for all decisions. Meticulously review this plan before execution. Always read and analyze files thoroughly before making any modifications. Follow this plan step-by-step without deviation.

## Overview
Goal: Ensure full context and assess impact before changes.

## Tasks

### 0.1 Inventory all DB usages
- Search for "import sqlite3" and "getDatabase()" consumers across electron/main and scripts.
- Identify transaction patterns: serialize(), BEGIN/COMMIT/ROLLBACK SQL usage, custom withTransaction helpers.
- Note return types usage for RunResult (lastID, changes) to map to better-sqlite3 info object (lastInsertRowid, changes).

### 0.2 Establish performance baseline
- Record existing typical operations: app startup time to ready, CRUD of notes/books, timer operations.
- Optionally use benchmark-like loops to measure n=100/1000 row inserts/selects.

### 0.3 Prepare rollback plan
- Tag a git branch for the current sqlite3 implementation (e.g., branch sqlite3-baseline).
- Back up local databases: noti-database.sqlite and WAL files.

## Rollback Strategy

### Git Branch Management
```bash
# Create baseline branch before starting migration
git checkout -b sqlite3-baseline
git tag v1.0-sqlite3-stable

# Create migration branch
git checkout main
git checkout -b feature/better-sqlite3-migration
```

### Database Backup Strategy
```bash
# Backup database files before migration
cp ~/.config/noti/noti-database.sqlite ~/.config/noti/noti-database.sqlite.backup
cp ~/.config/noti/noti-database.sqlite-wal ~/.config/noti/noti-database.sqlite-wal.backup
cp ~/.config/noti/noti-database.sqlite-shm ~/.config/noti/noti-database.sqlite-shm.backup
```

### Rollback Procedure
If migration issues occur:

1. **Immediate rollback:**
   ```bash
   git checkout sqlite3-baseline
   npm install  # Restore sqlite3 dependencies
   ```

2. **Database restoration:**
   ```bash
   cp ~/.config/noti/noti-database.sqlite.backup ~/.config/noti/noti-database.sqlite
   cp ~/.config/noti/noti-database.sqlite-wal.backup ~/.config/noti/noti-database.sqlite-wal
   cp ~/.config/noti/noti-database.sqlite-shm.backup ~/.config/noti/noti-database.sqlite-shm
   ```

3. **Verify rollback:**
   - Test application startup
   - Verify all data is intact
   - Run basic CRUD operations

### Rollback Decision Criteria
Consider rollback if:
- **Critical functionality broken** after 2+ hours of debugging
- **Data corruption detected** during testing
- **Performance significantly degraded** (>20% slower)
- **Build/deployment issues** cannot be resolved quickly
- **Timeline constraints** require immediate stability

## Dependencies
- None (this is the starting phase)

## Prerequisites
- Clean working directory
- All current changes committed
- Access to database files for backup

## Deliverables
- Complete inventory of all sqlite3 usage patterns
- Performance baseline measurements
- Git branches and tags created
- Database backups completed
- Rollback plan validated and ready
