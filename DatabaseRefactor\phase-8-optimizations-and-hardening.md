# Phase 8: Optimizations and Hardening

**AI Agent Instructions**: Use extended thinking for all decisions. Meticulously review this plan before execution. Always read and analyze files thoroughly before making any modifications. Follow this plan step-by-step without deviation.

## Overview
**Goal:** Optimize performance and harden the database layer for production use

**Complexity:** MEDIUM - Performance tuning and reliability improvements

## Tasks

### 8.1 Pragmas tailored for performance
Implement and tune performance-oriented pragma settings:

```typescript
// Core performance pragmas
db.pragma('journal_mode = WAL');
db.pragma('cache_size = 32000'); // Example from docs; tune based on memory/dataset
db.pragma('foreign_keys = ON');

// Additional performance pragmas to consider
db.pragma('synchronous = NORMAL'); // Balance between safety and speed
db.pragma('temp_store = MEMORY'); // Store temporary tables in memory
db.pragma('mmap_size = 268435456'); // 256MB memory-mapped I/O
```

**Tuning considerations:**
- Monitor memory usage with larger cache sizes
- Test synchronous settings for data safety vs performance balance
- Adjust mmap_size based on available system memory

### 8.2 Statement caching
Implement a small LRU cache or simple Map cache for hot queries. Reuse prepared statements wherever loops execute repeated operations.

```typescript
// Simple statement cache implementation
const statementCache = new Map<string, Database.Statement>();
const MAX_CACHE_SIZE = 100;

function getStatement(sql: string): Database.Statement {
  if (statementCache.has(sql)) {
    return statementCache.get(sql)!;
  }
  
  // If cache is full, remove oldest entry
  if (statementCache.size >= MAX_CACHE_SIZE) {
    const firstKey = statementCache.keys().next().value;
    statementCache.delete(firstKey);
  }
  
  const stmt = getDatabase().prepare(sql);
  statementCache.set(sql, stmt);
  return stmt;
}
```

**Cache strategy:**
- Identify frequently used queries through profiling
- Implement cache for SELECT, INSERT, UPDATE, DELETE patterns
- Monitor cache hit rates and adjust size accordingly

### 8.3 BigInt handling (if needed)
If IDs or counters might exceed Number safe range (unlikely here), consider:
- db.defaultSafeIntegers(true) globally.
- Or stmt.safeIntegers(true) for specific queries.

**Assessment:**
- Review current ID ranges and growth patterns
- Determine if BigInt support is necessary
- Implement only if justified by data requirements

### 8.4 Unsafe mode review
Avoid enabling unsafe mode unless profiling indicates a need and risk is understood:
- db.unsafeMode(true/false) toggles constraints; keep OFF by default.

**Safety considerations:**
- Only consider for specific high-performance scenarios
- Thoroughly test data integrity if enabled
- Document any unsafe mode usage clearly

### 8.5 Backup support
Offer db.backup('path') integration point for export/backup features.

```typescript
// Backup utility function
export const createDatabaseBackup = (backupPath: string): void => {
  const db = getDatabase();
  const backup = db.backup(backupPath);
  
  // Optional: Progress monitoring for large databases
  let progress;
  do {
    progress = backup.step(100); // Copy 100 pages at a time
    console.log(`Backup progress: ${progress.totalPages - progress.remainingPages}/${progress.totalPages} pages`);
  } while (progress.remainingPages > 0);
  
  backup.close();
  console.log(`Database backup created: ${backupPath}`);
};
```

### 8.6 Error handling and observability
Wrap DB calls at boundaries with try/catch; log SqliteError.code for context.
Provide health checks: simple SELECT 1 helper to detect database availability.

```typescript
// Database health check
export const checkDatabaseHealth = (): boolean => {
  try {
    const db = getDatabase();
    const result = db.prepare('SELECT 1 as health').get();
    return result?.health === 1;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
};

// Enhanced error logging
export const logDatabaseError = (error: any, context: string): void => {
  if (error.code) {
    console.error(`Database error in ${context}:`, {
      code: error.code,
      message: error.message,
      stack: error.stack
    });
  } else {
    console.error(`Unknown error in ${context}:`, error);
  }
};
```

### 8.7 Connection monitoring and maintenance
Implement monitoring for database connection health and WAL file management:

```typescript
// WAL checkpoint management
export const checkpointWAL = (): void => {
  const db = getDatabase();
  const result = db.pragma('wal_checkpoint(RESTART)');
  console.log('WAL checkpoint result:', result);
};

// Monitor WAL file size
export const monitorWALSize = (): number => {
  const db = getDatabase();
  const walInfo = db.pragma('wal_checkpoint');
  return walInfo.busy + walInfo.log; // Total WAL pages
};
```

### 8.8 Performance monitoring integration
Add hooks for performance monitoring and metrics collection:

```typescript
// Query performance monitoring
export const withPerformanceMonitoring = <T>(
  operation: () => T,
  operationName: string
): T => {
  const startTime = performance.now();
  try {
    const result = operation();
    const duration = performance.now() - startTime;
    console.log(`${operationName} completed in ${duration.toFixed(2)}ms`);
    return result;
  } catch (error) {
    const duration = performance.now() - startTime;
    console.error(`${operationName} failed after ${duration.toFixed(2)}ms:`, error);
    throw error;
  }
};
```

## Dependencies
- Phase 7 must be completed (integration testing passed)

## Prerequisites
- All migration phases completed successfully
- Performance baseline established from Phase 7
- System stable and functional

## Deliverables
- Tuned pragmas, optional statement caching, clear error logs, optional backup utilities.
- Performance monitoring and health check systems implemented.
- WAL management and maintenance procedures established.
- Documentation of optimization settings and their impact.
- Measurable performance improvements documented.
