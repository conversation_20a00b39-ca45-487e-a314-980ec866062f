// Database API with CRUD operations
import { getDatabase } from './database';
import type Database from 'better-sqlite3';
import * as fs from 'fs';

// Types
// No need for separate RunResult type - use return type directly
type RunResult = { changes: number; lastInsertRowid: number; lastID: number };

// Define interfaces for data structures
export interface Note {
  id?: number;
  title: string;
  content?: string;
  html_content?: string | null;
  folder_id?: number | null;
  book_id?: number | null;
  type?: string;
  color?: string | null;
  order?: number | null;
  last_viewed_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface Folder {
  id?: number;
  name: string;
  parent_id?: number | null;
  book_id?: number | null;
  color?: string | null;
  order?: number | null;
  created_at?: string;
  updated_at?: string;
}

export interface FolderWithNoteCount extends Folder {
  notesCount: number;
}

export interface DbResult {
  id?: number;
  changes?: number;
}

export interface Book {
  id?: number;
  title: string;
  author?: string | null;
  isbn?: string | null;
  cover_url?: string | null;
  publication_date?: string | null;
  description?: string | null;
  page_count?: number | null;
  current_page?: number | null;
  rating?: number | null;
  language?: string | null;
  genres?: string | null;
  olid?: string | null;
  status?: string | null;
  custom_fields?: string | null;
  created_at?: string;
  updated_at?: string;
}

export interface BookWithNoteCount extends Book {
  notesCount: number;
  recentNote?: {
    id: number;
    title: string;
    last_viewed_at: string;
  };
  addedDate?: Date;
  cover_media_url?: string | null;
}

// Helper function to run queries that return a single result
export const dbGet = <T>(query: string, params: any[] = []): T | undefined => {
  try {
    const db = getDatabase();
    const stmt = db.prepare(query);
    return stmt.get(...params) as T | undefined;
  } catch (error) {
    console.error('Error in dbGet:', error);
    throw error;
  }
};

// Helper function to run queries that return multiple results
export const dbAll = <T>(query: string, params: any[] = []): T[] => {
  try {
    const db = getDatabase();
    const stmt = db.prepare(query);
    return stmt.all(...params) as T[];
  } catch (error) {
    console.error('Error in dbAll:', error);
    throw error;
  }
};

// Helper function to run queries that don't return a result (INSERT, UPDATE, DELETE)
export const dbRun = (query: string, params: any[] = []): RunResult => {
  try {
    const db = getDatabase();
    const stmt = db.prepare(query);
    const info = stmt.run(...params);
    // Provide compatibility wrapper for lastID
    return {
      changes: info.changes,
      lastInsertRowid: Number(info.lastInsertRowid),
      lastID: Number(info.lastInsertRowid)
    };
  } catch (error) {
    console.error('Error in dbRun:', error);
    throw error;
  }
};

// NOTES CRUD OPERATIONS

// Create a new note
export const createNote = (note: Note): Note => {
  // Input validation
  if (!note.title || typeof note.title !== 'string' || note.title.trim() === '') {
    throw new Error('Note title is required and must be a non-empty string');
  }

  if (note.title.length > 255) {
    throw new Error('Note title must not exceed 255 characters');
  }

  if (note.content && typeof note.content !== 'string') {
    throw new Error('Note content must be a string');
  }

  if (note.html_content && typeof note.html_content !== 'string') {
    throw new Error('Note HTML content must be a string');
  }

  if (note.folder_id !== null && note.folder_id !== undefined && (!Number.isInteger(note.folder_id) || note.folder_id <= 0)) {
    throw new Error('Folder ID must be a positive integer');
  }

  if (note.book_id !== null && note.book_id !== undefined && (!Number.isInteger(note.book_id) || note.book_id <= 0)) {
    throw new Error('Book ID must be a positive integer');
  }

  const {
    title,
    content,
    html_content = null,
    folder_id = null,
    book_id = null,
    type = 'text',
    color = null,
    order = null
  } = note;

  // Use ISO timestamp format for consistency with backup system
  const now = new Date().toISOString();
  const query = `
    INSERT INTO notes (
      title, content, html_content, folder_id, book_id,
      type, color, "order", last_viewed_at, created_at, updated_at
    )
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  try {
    const result: RunResult = dbRun(query, [
      title.trim(), content, html_content, folder_id, book_id,
      type, color, order, now, now, now
    ]);

    // Return the created note using lastID
    if (result.lastID) {
      return getNoteById(result.lastID);
    }
    throw new Error('Failed to create note, no ID returned');
  } catch (error) {
    console.error('Error creating note:', error);
    throw error;
  }
};

// Get all notes
export const getAllNotes = (): Note[] => {
  const query = 'SELECT * FROM notes ORDER BY updated_at DESC';

  try {
    return dbAll<Note>(query);
  } catch (error) {
    console.error('Error getting all notes:', error);
    throw error;
  }
};

// Get a note by ID
export const getNoteById = (id: number): Note => {
  const query = 'SELECT * FROM notes WHERE id = ?';

  try {
    const result = dbGet<Note>(query, [id]);
    if (!result) {
      throw new Error(`Note with ID ${id} not found`);
    }
    return result;
  } catch (error) {
    console.error(`Error getting note with ID ${id}:`, error);
    throw error;
  }
};

// Get notes by folder ID
export const getNotesByFolderId = (folderId: number): Note[] => {
  const query = 'SELECT * FROM notes WHERE folder_id = ? ORDER BY updated_at DESC';

  try {
    return dbAll<Note>(query, [folderId]);
  } catch (error) {
    console.error(`Error getting notes for folder ID ${folderId}:`, error);
    throw error;
  }
};

// Get notes by book ID
export const getNotesByBookId = (bookId: number): Note[] => {
  const query = 'SELECT * FROM notes WHERE book_id = ? ORDER BY updated_at DESC';

  try {
    return dbAll<Note>(query, [bookId]);
  } catch (error) {
    console.error(`Error getting notes for book ID ${bookId}:`, error);
    throw error;
  }
};

// Update a note
export const updateNote = (id: number, noteUpdates: Partial<Note>): Note => {
  const {
    title,
    content,
    html_content,
    folder_id,
    book_id,
    type,
    color,
    order,
    last_viewed_at
  } = noteUpdates;

  // Build update query dynamically based on provided fields
  // Use ISO timestamp format for consistency with backup system
  let query = 'UPDATE notes SET updated_at = ?';
  const params: any[] = [new Date().toISOString()];

  if (title !== undefined) {
    query += ', title = ?';
    params.push(title);
  }

  if (content !== undefined) {
    query += ', content = ?';
    params.push(content);
  }

  if (html_content !== undefined) {
    query += ', html_content = ?';
    params.push(html_content);
  }

  if (folder_id !== undefined) {
    query += ', folder_id = ?';
    params.push(folder_id);
  }

  if (book_id !== undefined) {
    query += ', book_id = ?';
    params.push(book_id);
  }

  if (type !== undefined) {
    query += ', type = ?';
    params.push(type);
  }

  if (color !== undefined) {
    query += ', color = ?';
    params.push(color);
  }

  if (order !== undefined) {
    query += ', "order" = ?';
    params.push(order);
  }

  if (last_viewed_at !== undefined) {
    query += ', last_viewed_at = ?';
    params.push(last_viewed_at);
  } else {
    // Update the last_viewed_at when a note is updated - use ISO string for consistency
    query += ', last_viewed_at = ?';
    params.push(new Date().toISOString());
  }

  query += ' WHERE id = ?';
  params.push(id);

  try {
    const result: RunResult = dbRun(query, params);
    if (result.changes && result.changes > 0) {
      return getNoteById(id);
    }
    throw new Error(`Note with ID ${id} not found or no changes made`);
  } catch (error) {
    console.error(`Error updating note with ID ${id}:`, error);
    throw error;
  }
};

// Delete a note
export const deleteNote = (id: number): { success: boolean; id: number } => {
  const query = 'DELETE FROM notes WHERE id = ?';

  try {
    const result: RunResult = dbRun(query, [id]);
    return { success: result.changes ? result.changes > 0 : false, id };
  } catch (error) {
    console.error(`Error deleting note with ID ${id}:`, error);
    throw error;
  }
};

// FOLDERS CRUD OPERATIONS

// Create a new folder
export const createFolder = (folder: Folder): Folder => {
  const { name, parent_id = null, book_id = null, color = null, order = null } = folder;
  // Use ISO timestamp format for consistency with backup system
  const now = new Date().toISOString();
  const query = `
    INSERT INTO folders (name, parent_id, book_id, color, "order", created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?)
  `;

  try {
    const result: RunResult = dbRun(query, [name, parent_id, book_id, color, order, now, now]);
    // Return the created folder using lastID
    if (result.lastID) {
      return getFolderById(result.lastID);
    }
    throw new Error('Failed to create folder, no ID returned');
  } catch (error) {
    console.error('Error creating folder:', error);
    throw error;
  }
};

// Get all folders
export const getAllFolders = (): Folder[] => {
  const query = 'SELECT * FROM folders ORDER BY name';

  try {
    return dbAll<Folder>(query);
  } catch (error) {
    console.error('Error getting all folders:', error);
    throw error;
  }
};

// Get all folders with their note counts
export const getAllFoldersWithNoteCounts = (): FolderWithNoteCount[] => {
  const query = `
    SELECT
        f.*,
        COUNT(n.id) as notesCount
    FROM
        folders f
    LEFT JOIN
        notes n ON f.id = n.folder_id
    GROUP BY
        f.id
    ORDER BY
        f.name;
  `;
  try {
    // The result from SQL will have notesCount, so we cast to FolderWithNoteCount
    return dbAll<FolderWithNoteCount>(query);
  } catch (error) {
    console.error('Error getting all folders with note counts:', error);
    throw error;
  }
};

// Get a folder by ID
export const getFolderById = (id: number): Folder => {
  // Validate folder ID
  if (id === 0 || !Number.isInteger(id) || id < 0) {
    console.error(`Invalid folder ID: ${id}`);
    throw new Error(`Folder with ID ${id} not found`);
  }

  const query = 'SELECT * FROM folders WHERE id = ?';

  try {
    const result = dbGet<Folder>(query, [id]);
    if (!result) {
      throw new Error(`Folder with ID ${id} not found`);
    }
    return result;
  } catch (error) {
    console.error(`Error getting folder with ID ${id}:`, error);
    throw error;
  }
};

// Get child folders
export const getChildFolders = (parentId: number | null): Folder[] => {
  let query: string;
  let params: any[] = [];

  if (parentId === null) {
    // Get root folders (with no parent)
    query = 'SELECT * FROM folders WHERE parent_id IS NULL ORDER BY name';
  } else {
    // Get child folders of a specific parent
    query = 'SELECT * FROM folders WHERE parent_id = ? ORDER BY name';
    params = [parentId];
  }

  try {
    return dbAll<Folder>(query, params);
  } catch (error) {
    console.error('Error getting child folders:', error);
    throw error;
  }
};

// Get folder by book_id
export const getFolderByBookId = (bookId: number): Folder | null => {
  const query = 'SELECT * FROM folders WHERE book_id = ?';

  try {
    const result = dbGet<Folder>(query, [bookId]);
    return result || null;
  } catch (error) {
    console.error(`Error getting folder for book ID ${bookId}:`, error);
    throw error;
  }
};

// Get inherited book_id by traversing up the folder hierarchy
export const getInheritedBookId = (parentId: number | null): number | null => {
  if (parentId === null || parentId === undefined) {
    return null;
  }

  try {
    let currentFolderId: number | null = parentId;
    const maxDepth = 50; // Prevent infinite loops
    let depth = 0;

    while (currentFolderId !== null && depth < maxDepth) {
      const folder = getFolderById(currentFolderId);

      // If this folder has a book_id, return it
      if (folder.book_id !== null && folder.book_id !== undefined) {
        console.log(`Found inherited book_id ${folder.book_id} from folder "${folder.name}" (ID: ${folder.id})`);
        return folder.book_id;
      }

      // Move up to the parent folder
      currentFolderId = folder.parent_id;
      depth++;
    }

    // No book_id found in the hierarchy
    return null;
  } catch (error) {
    console.error(`Error getting inherited book_id for parent ID ${parentId}:`, error);
    return null;
  }
};

// Update a folder
export const updateFolder = (id: number, folderUpdates: Partial<Folder>): Folder => {
  const { name, parent_id, book_id, color, order } = folderUpdates;

  // Build update query dynamically based on provided fields
  // Use ISO timestamp format for consistency with backup system
  let query = 'UPDATE folders SET updated_at = ?';
  const params: any[] = [new Date().toISOString()];

  if (name !== undefined) {
    query += ', name = ?';
    params.push(name);
  }

  if (parent_id !== undefined) {
    query += ', parent_id = ?';
    params.push(parent_id);
  }

  if (book_id !== undefined) {
    query += ', book_id = ?';
    params.push(book_id);
  }

  if (color !== undefined) {
    query += ', color = ?';
    params.push(color);
  }

  if (order !== undefined) {
    query += ', "order" = ?';
    params.push(order);
  }

  query += ' WHERE id = ?';
  params.push(id);

  try {
    const result: RunResult = dbRun(query, params);
    if (result.changes && result.changes > 0) {
      return getFolderById(id);
    }
    throw new Error(`Folder with ID ${id} not found or no changes made`);
  } catch (error) {
    console.error(`Error updating folder with ID ${id}:`, error);
    throw error;
  }
};

// Delete a folder and its notes
export const deleteFolder = (id: number): { success: boolean; id: number } => {
  // Note: We're relying on ON DELETE CASCADE for child folders and
  // ON DELETE SET NULL for notes in this folder
  const query = 'DELETE FROM folders WHERE id = ?';

  try {
    const result: RunResult = dbRun(query, [id]);
    return { success: result.changes ? result.changes > 0 : false, id };
  } catch (error) {
    console.error(`Error deleting folder with ID ${id}:`, error);
    throw error;
  }
};

// BOOKS CRUD OPERATIONS

// Create a new book
export const createBook = (book: Book): Book => {
  const {
    title,
    author = null,
    isbn = null,
    cover_url = null,
    publication_date = null,
    description = null,
    page_count = null,
    current_page = null,
    rating = null,
    language = null,
    genres = null,
    olid = null,
    status = 'unread',
    custom_fields = null
  } = book;

  // Use ISO timestamp format for consistency with backup system
  const now = new Date().toISOString();
  const query = `
    INSERT INTO books (
      title, author, isbn, cover_url, publication_date,
      description, page_count, current_page, rating, language, genres,
      olid, status, custom_fields, created_at, updated_at
    )
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  const params = [
    title, author, isbn, cover_url, publication_date,
    description, page_count, current_page, rating, language, genres,
    olid, status, custom_fields, now, now
  ];

  try {
    const result: RunResult = dbRun(query, params);

    // Return the created book using lastID
    if (result.lastID) {
      return getBookById(result.lastID);
    }
    throw new Error('Failed to create book, no ID returned');
  } catch (error) {
    console.error('Error creating book:', error);
    throw error;
  }
};

// Get all books
export const getAllBooks = (): Book[] => {
  const query = 'SELECT * FROM books ORDER BY created_at DESC';

  try {
    return dbAll<Book>(query);
  } catch (error) {
    console.error('Error getting all books:', error);
    throw error;
  }
};

// Get all books with their note counts and recent note info
export const getAllBooksWithNoteCounts = (): BookWithNoteCount[] => {
  const query = `
    SELECT
      b.*,
      COUNT(n.id) as notesCount,
      rn.id as recent_note_id,
      rn.title as recent_note_title,
      rn.last_viewed_at as recent_note_last_viewed_at
    FROM
      books b
    LEFT JOIN
      notes n ON b.id = n.book_id
    LEFT JOIN (
      SELECT
        book_id,
        id,
        title,
        last_viewed_at,
        ROW_NUMBER() OVER (PARTITION BY book_id ORDER BY last_viewed_at DESC) as rn
      FROM notes
      WHERE book_id IS NOT NULL
    ) rn ON b.id = rn.book_id AND rn.rn = 1
    GROUP BY
      b.id
    ORDER BY
      b.created_at DESC;
  `;

  try {
    const results = dbAll<any>(query);

    // Transform the flat result into the proper structure
    return results.map(row => {
      const book: BookWithNoteCount = {
        id: row.id,
        title: row.title,
        author: row.author,
        isbn: row.isbn,
        cover_url: row.cover_url,
        publication_date: row.publication_date,
        description: row.description,
        page_count: row.page_count,
        current_page: row.current_page,
        rating: row.rating,
        language: row.language,
        genres: row.genres,
        olid: row.olid,
        status: row.status,
        custom_fields: row.custom_fields,
        created_at: row.created_at,
        updated_at: row.updated_at,
        notesCount: row.notesCount
      };

      // Add recent note if it exists
      if (row.recent_note_id) {
        book.recentNote = {
          id: row.recent_note_id,
          title: row.recent_note_title,
          last_viewed_at: row.recent_note_last_viewed_at
        };
      }

      return book;
    });
  } catch (error) {
    console.error('Error getting all books with note counts:', error);
    throw error;
  }
};

// Get a book by ID
export const getBookById = (id: number): Book => {
  const query = 'SELECT * FROM books WHERE id = ?';

  try {
    const result = dbGet<Book>(query, [id]);
    if (!result) {
      throw new Error(`Book with ID ${id} not found`);
    }
    return result;
  } catch (error) {
    console.error(`Error getting book with ID ${id}:`, error);
    throw error;
  }
};

// Get book by ISBN
export const getBookByIsbn = (isbn: string): Book | null => {
  const query = 'SELECT * FROM books WHERE isbn = ?';

  try {
    const result = dbGet<Book>(query, [isbn]);
    return result || null;
  } catch (error) {
    console.error(`Error getting book with ISBN ${isbn}:`, error);
    throw error;
  }
};

// Get book by OpenLibrary ID
export const getBookByOlid = (olid: string): Book | null => {
  const query = 'SELECT * FROM books WHERE olid = ?';

  try {
    const result = dbGet<Book>(query, [olid]);
    return result || null;
  } catch (error) {
    console.error(`Error getting book with OLID ${olid}:`, error);
    throw error;
  }
};

// Search books by title, author, or ISBN
export const searchBooks = (searchTerm: string): Book[] => {
  const query = `
    SELECT * FROM books
    WHERE
      title LIKE ? OR
      author LIKE ? OR
      isbn LIKE ? OR
      description LIKE ?
    ORDER BY
      CASE
        WHEN title LIKE ? THEN 1
        WHEN author LIKE ? THEN 2
        WHEN isbn LIKE ? THEN 3
        ELSE 4
      END,
      created_at DESC
  `;

  const searchPattern = `%${searchTerm}%`;
  const exactSearchPattern = `${searchTerm}%`;

  try {
    return dbAll<Book>(query, [
      searchPattern, searchPattern, searchPattern, searchPattern,
      exactSearchPattern, exactSearchPattern, exactSearchPattern
    ]);
  } catch (error) {
    console.error(`Error searching books with term "${searchTerm}":`, error);
    throw error;
  }
};

// Get recent books (added in the last N days)
export const getRecentBooks = (days: number = 14): Book[] => {
  const query = `
    SELECT * FROM books
    WHERE created_at >= ?
    ORDER BY created_at DESC
  `;

  // Calculate the cutoff date
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - days);
  const cutoffDateString = cutoffDate.toISOString();

  try {
    return dbAll<Book>(query, [cutoffDateString]);
  } catch (error) {
    console.error(`Error getting recent books from last ${days} days:`, error);
    throw error;
  }
};

// Update a book
export const updateBook = (id: number, bookUpdates: Partial<Book>): Book => {
  const {
    title,
    author,
    isbn,
    cover_url,
    publication_date,
    description,
    page_count,
    current_page,
    rating,
    language,
    genres,
    olid,
    status,
    custom_fields
  } = bookUpdates;

  // Build update query dynamically based on provided fields
  // Use ISO timestamp format for consistency with backup system
  let query = 'UPDATE books SET updated_at = ?';
  const params: any[] = [new Date().toISOString()];

  if (title !== undefined) {
    query += ', title = ?';
    params.push(title);
  }

  if (author !== undefined) {
    query += ', author = ?';
    params.push(author);
  }

  if (isbn !== undefined) {
    query += ', isbn = ?';
    params.push(isbn);
  }

  // DEFENSIVE FIX: Only update cover_url if explicitly provided and not null/empty
  // This prevents accidental deletion of existing covers during regular book updates
  if (cover_url !== undefined && cover_url !== null && cover_url !== '') {
    query += ', cover_url = ?';
    params.push(cover_url);
  } else if (cover_url === null || cover_url === '') {
    // Only explicitly set to null if that's the intention (e.g., removing a cover)
    console.log(`Warning: Attempting to clear cover_url for book ID ${id}. This should be intentional.`);
    query += ', cover_url = ?';
    params.push(cover_url);
  }
  // If cover_url is undefined, we don't update it at all (preserve existing value)

  if (publication_date !== undefined) {
    query += ', publication_date = ?';
    params.push(publication_date);
  }

  if (description !== undefined) {
    query += ', description = ?';
    params.push(description);
  }

  if (page_count !== undefined) {
    query += ', page_count = ?';
    params.push(page_count);
  }

  if (current_page !== undefined) {
    query += ', current_page = ?';
    params.push(current_page);
  }

  if (rating !== undefined) {
    query += ', rating = ?';
    params.push(rating);
  }

  if (language !== undefined) {
    query += ', language = ?';
    params.push(language);
  }

  if (genres !== undefined) {
    query += ', genres = ?';
    params.push(genres);
  }

  if (olid !== undefined) {
    query += ', olid = ?';
    params.push(olid);
  }

  if (status !== undefined) {
    query += ', status = ?';
    params.push(status);
  }

  if (custom_fields !== undefined) {
    query += ', custom_fields = ?';
    params.push(custom_fields);
  }

  query += ' WHERE id = ?';
  params.push(id);

  try {
    const result: RunResult = dbRun(query, params);
    if (result.changes && result.changes > 0) {
      return getBookById(id);
    }
    throw new Error(`Book with ID ${id} not found or no changes made`);
  } catch (error) {
    console.error(`Error updating book with ID ${id}:`, error);
    throw error;
  }
};

// Delete a book
// This function now also needs to handle deleting associated media files
// and unlinking notes (or deleting them if they are exclusively for this book and not in a folder)
export const deleteBook = (id: number): { success: boolean; id: number } => {
  const db: Database = getDatabase();

  // Use better-sqlite3 transaction API
  const deleteBookTransaction = db.transaction((bookId: number) => {
    // 1. Delete associated media files (cover image and any other media linked to this book)
    // Query all media files linked to this book
    const mediaFilesQuery = 'SELECT * FROM media_files WHERE book_id = ?';
    const mediaFiles = dbAll<{id: number; file_path: string; file_name: string}>(mediaFilesQuery, [bookId]);

    // Collect file paths for deletion after transaction
    const filesToDelete: string[] = [];

    // Delete each media file (database record first)
    for (const mediaFile of mediaFiles) {
      try {
        // Delete from database first
        const deleteMediaQuery = 'DELETE FROM media_files WHERE id = ?';
        dbRun(deleteMediaQuery, [mediaFile.id]);

        // Collect file path for later deletion
        if (fs.existsSync(mediaFile.file_path)) {
          filesToDelete.push(mediaFile.file_path);
        }
      } catch (mediaError) {
        console.error(`Error deleting media file ${mediaFile.id} for book ${bookId}:`, mediaError);
        // Continue with other files even if one fails
      }
    }

    console.log(`Deleted ${mediaFiles.length} media file(s) from database for book ${bookId}`);

    // 2. Handle notes associated with the book but NOT in a specific book folder
    // Notes directly linked to the book (book_id = id) and not in any folder (folder_id IS NULL)
    // or in a folder that is NOT the book's dedicated folder should be considered for deletion or unlinking.
    // The new logic in books-api.ts (deleteBookAndHandleFolder) moves the book's folder to root
    // and unlinks notes within that folder from the book (sets book_id to NULL).
    // So, notes that are *only* linked via book_id and are *not* in that book's folder might be orphaned.
    // The current schema for notes has ON DELETE SET NULL for book_id, so they will be unlinked automatically if not handled otherwise.
    // Let's ensure notes that were *only* tied to this book and not in its folder are deleted.
    // However, the requirement is that notes in the book's folder are preserved and the folder moved.
    // The `deleteBookAndHandleFolder` in `books-api.ts` already handles notes within the book's specific folder.
    // So, the database's ON DELETE SET NULL for `notes.book_id` should suffice for other notes.
    console.log(`Notes linked to book ${bookId} will have their book_id set to NULL due to database constraints (ON DELETE SET NULL).`);

    // 3. Delete the book itself
    const bookDeleteQuery = 'DELETE FROM books WHERE id = ?';
    const result: RunResult = dbRun(bookDeleteQuery, [bookId]);

    // Return result and files to delete
    return {
      success: result.changes ? result.changes > 0 : false,
      id: bookId,
      filesToDelete
    };
  });

  try {
    // Execute the transaction
    const result = deleteBookTransaction(id);

    // Delete physical files after successful transaction
    if (result.filesToDelete) {
      for (const filePath of result.filesToDelete) {
        try {
          fs.unlinkSync(filePath);
          console.log(`Deleted physical file: ${filePath}`);
        } catch (fileError) {
          console.error(`Error deleting physical file ${filePath}:`, fileError);
          // Don't fail the entire operation for file deletion errors
        }
      }
    }

    return { success: result.success, id: result.id };
  } catch (error) {
    console.error(`Error deleting book with ID ${id}:`, error);
    throw error;
  }
};

// TRANSACTION HELPER FUNCTIONS

/**
 * Execute a function within a database transaction
 * Provides automatic commit/rollback handling using better-sqlite3's transaction API
 */
export const withTransaction = <T>(operation: () => T): T => {
  const db: Database = getDatabase();
  const tx = db.transaction(operation);
  return tx();
};

/**
 * Execute multiple database queries within a single transaction
 * Ensures all queries see the same consistent database state
 * Uses immediate mode for consistent snapshot behavior
 */
export const withReadTransaction = <T>(operation: () => T): T => {
  const db: Database = getDatabase();
  const tx = db.transaction(operation).immediate();
  return tx();
};

// STATEMENT CACHING (OPTIONAL OPTIMIZATION)

/**
 * Statement cache for frequently used queries
 * Improves performance by reusing prepared statements
 */
const statementCache = new Map<string, Database.Statement>();

/**
 * Get a cached prepared statement or create and cache a new one
 * @param sql SQL query string
 * @returns Prepared statement
 */
export const prepareCached = (sql: string): Database.Statement => {
  if (!statementCache.has(sql)) {
    const db = getDatabase();
    statementCache.set(sql, db.prepare(sql));
  }
  return statementCache.get(sql)!;
};

/**
 * Clear the statement cache (useful for cleanup or testing)
 * Note: better-sqlite3 uses automatic garbage collection for statement cleanup
 */
export const clearStatementCache = (): void => {
  statementCache.clear();
};

/**
 * Enhanced dbGet using statement caching for frequently used queries
 */
export const dbGetCached = <T>(query: string, params: any[] = []): T | undefined => {
  try {
    const stmt = prepareCached(query);
    return stmt.get(...params) as T | undefined;
  } catch (error) {
    console.error('Error in dbGetCached:', error);
    throw error;
  }
};

/**
 * Enhanced dbAll using statement caching for frequently used queries
 */
export const dbAllCached = <T>(query: string, params: any[] = []): T[] => {
  try {
    const stmt = prepareCached(query);
    return stmt.all(...params) as T[];
  } catch (error) {
    console.error('Error in dbAllCached:', error);
    throw error;
  }
};

// DATABASE HEALTH AND MONITORING UTILITIES

/**
 * Database health check - verifies database connectivity and basic functionality
 * @returns boolean indicating if database is healthy
 */
export const checkDatabaseHealth = (): boolean => {
  try {
    const db = getDatabase();
    const result = db.prepare('SELECT 1 as health').get();
    return result?.health === 1;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
};

/**
 * Enhanced error logging for database operations
 * @param error - The error object to log
 * @param context - Context string describing where the error occurred
 */
export const logDatabaseError = (error: any, context: string): void => {
  if (error && typeof error === 'object' && 'code' in error) {
    console.error(`[Database] Error in ${context}:`, {
      code: error.code,
      message: error.message,
      stack: error.stack
    });
  } else {
    console.error(`[Database] Unknown error in ${context}:`, error);
  }
};

/**
 * Get database statistics and information
 * @returns Object containing database statistics
 */
export const getDatabaseStats = (): any => {
  try {
    const db = getDatabase();
    return {
      pageCount: db.pragma('page_count'),
      pageSize: db.pragma('page_size'),
      cacheSize: db.pragma('cache_size'),
      journalMode: db.pragma('journal_mode'),
      synchronous: db.pragma('synchronous'),
      foreignKeys: db.pragma('foreign_keys'),
      tempStore: db.pragma('temp_store'),
      mmapSize: db.pragma('mmap_size'),
      walAutocheckpoint: db.pragma('wal_autocheckpoint')
    };
  } catch (error) {
    logDatabaseError(error, 'getDatabaseStats');
    throw error;
  }
};

// DATABASE BACKUP UTILITIES

/**
 * Create a backup of the database using better-sqlite3's native backup API
 * @param backupPath - Path where the backup should be created
 * @param options - Optional backup configuration
 * @returns Promise that resolves when backup is complete
 */
export const createDatabaseBackup = async (
  backupPath: string,
  options?: {
    progress?: (info: { totalPages: number; remainingPages: number }) => void;
    pageSize?: number;
  }
): Promise<void> => {
  try {
    const db = getDatabase();

    // Use better-sqlite3's native backup method
    const backup = db.backup(backupPath, {
      progress: options?.progress ? (info) => {
        options.progress!({
          totalPages: info.totalPages,
          remainingPages: info.remainingPages
        });
      } : undefined
    });

    await backup;
    console.log(`Database backup created successfully at: ${backupPath}`);
  } catch (error) {
    logDatabaseError(error, 'createDatabaseBackup');
    throw new Error(`Failed to create database backup: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Verify a backup file by attempting to open it and run a basic query
 * @param backupPath - Path to the backup file to verify
 * @returns boolean indicating if backup is valid
 */
export const verifyDatabaseBackup = (backupPath: string): boolean => {
  try {
    // Import Database here to avoid circular dependencies
    const Database = require('better-sqlite3');
    const backupDb = new Database(backupPath, { readonly: true });

    // Try to run a simple query to verify the backup
    const result = backupDb.prepare('SELECT COUNT(*) as count FROM sqlite_master').get();
    backupDb.close();

    return typeof result === 'object' && result !== null && 'count' in result;
  } catch (error) {
    logDatabaseError(error, 'verifyDatabaseBackup');
    return false;
  }
};

// WAL MANAGEMENT UTILITIES

/**
 * Manually checkpoint the WAL file to merge changes back to the main database
 * @param mode - Checkpoint mode: 'PASSIVE', 'FULL', 'RESTART', or 'TRUNCATE'
 * @returns Object with checkpoint results
 */
export const checkpointWAL = (mode: 'PASSIVE' | 'FULL' | 'RESTART' | 'TRUNCATE' = 'PASSIVE'): any => {
  try {
    const db = getDatabase();

    // Use pragma to perform WAL checkpoint
    const result = db.pragma(`wal_checkpoint(${mode})`);
    console.log(`WAL checkpoint (${mode}) completed:`, result);

    return result;
  } catch (error) {
    logDatabaseError(error, 'checkpointWAL');
    throw error;
  }
};

/**
 * Monitor WAL file size and return information about WAL usage
 * Note: This function performs a PASSIVE checkpoint as a side effect of monitoring.
 * PASSIVE checkpoints are non-blocking and only checkpoint if no readers are active.
 * @returns Object containing WAL size information
 */
export const monitorWALSize = (): { walPages: number; checkpointPages: number; autoCheckpoint: number } => {
  try {
    const db = getDatabase();

    // Get WAL-related information using pragmas
    // Note: wal_checkpoint(PASSIVE) has the side effect of performing a passive checkpoint
    // This is unavoidable with SQLite's current API for getting WAL size information
    const walPages = db.pragma('wal_checkpoint(PASSIVE)'); // Returns [busy, log, checkpointed]
    const autoCheckpoint = db.pragma('wal_autocheckpoint');

    // Parse the checkpoint result
    let checkpointInfo = { walPages: 0, checkpointPages: 0 };
    if (Array.isArray(walPages) && walPages.length >= 3) {
      checkpointInfo = {
        walPages: walPages[1] || 0,        // Number of pages in WAL
        checkpointPages: walPages[2] || 0  // Number of pages checkpointed
      };
    }

    return {
      walPages: checkpointInfo.walPages,
      checkpointPages: checkpointInfo.checkpointPages,
      autoCheckpoint: autoCheckpoint
    };
  } catch (error) {
    logDatabaseError(error, 'monitorWALSize');
    throw error;
  }
};

/**
 * Get WAL file information and suggest if checkpoint is needed
 * @param threshold - WAL page threshold for suggesting checkpoint (default: 1000)
 * @returns Object with WAL info and checkpoint recommendation
 */
export const getWALInfo = (threshold: number = 1000): {
  walPages: number;
  checkpointPages: number;
  autoCheckpoint: number;
  shouldCheckpoint: boolean;
  recommendation: string;
} => {
  try {
    const walInfo = monitorWALSize();
    const shouldCheckpoint = walInfo.walPages > threshold;

    let recommendation = 'WAL size is optimal';
    if (shouldCheckpoint) {
      recommendation = `WAL has ${walInfo.walPages} pages (>${threshold}). Consider running checkpoint.`;
    }

    return {
      ...walInfo,
      shouldCheckpoint,
      recommendation
    };
  } catch (error) {
    logDatabaseError(error, 'getWALInfo');
    throw error;
  }
};

// PERFORMANCE MONITORING UTILITIES

/**
 * Performance monitoring wrapper for database operations
 * @param operation - The database operation to monitor
 * @param operationName - Name of the operation for logging
 * @param slowThreshold - Threshold in milliseconds to consider operation slow (default: 100ms)
 * @returns The result of the operation
 */
export const withPerformanceMonitoring = <T>(
  operation: () => T,
  operationName: string,
  slowThreshold: number = 100
): T => {
  const start = performance.now();

  try {
    const result = operation();
    const duration = performance.now() - start;

    if (duration > slowThreshold) {
      console.warn(`[Database] Slow operation detected: ${operationName} took ${duration.toFixed(2)}ms`);
    } else {
      console.debug(`[Database] Operation completed: ${operationName} took ${duration.toFixed(2)}ms`);
    }

    return result;
  } catch (error) {
    const duration = performance.now() - start;
    console.error(`[Database] Operation failed: ${operationName} after ${duration.toFixed(2)}ms`, error);
    logDatabaseError(error, `withPerformanceMonitoring(${operationName})`);
    throw error;
  }
};

/**
 * Async version of performance monitoring wrapper
 * @param operation - The async database operation to monitor
 * @param operationName - Name of the operation for logging
 * @param slowThreshold - Threshold in milliseconds to consider operation slow (default: 100ms)
 * @returns Promise with the result of the operation
 */
export const withPerformanceMonitoringAsync = async <T>(
  operation: () => Promise<T>,
  operationName: string,
  slowThreshold: number = 100
): Promise<T> => {
  const start = performance.now();

  try {
    const result = await operation();
    const duration = performance.now() - start;

    if (duration > slowThreshold) {
      console.warn(`[Database] Slow async operation detected: ${operationName} took ${duration.toFixed(2)}ms`);
    } else {
      console.debug(`[Database] Async operation completed: ${operationName} took ${duration.toFixed(2)}ms`);
    }

    return result;
  } catch (error) {
    const duration = performance.now() - start;
    console.error(`[Database] Async operation failed: ${operationName} after ${duration.toFixed(2)}ms`, error);
    logDatabaseError(error, `withPerformanceMonitoringAsync(${operationName})`);
    throw error;
  }
};