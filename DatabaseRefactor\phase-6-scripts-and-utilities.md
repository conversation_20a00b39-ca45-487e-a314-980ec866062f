# Phase 6: Scripts and Utilities

**AI Agent Instructions**: Use extended thinking for all decisions. Meticulously review this plan before execution. Always read and analyze files thoroughly before making any modifications. Follow this plan step-by-step without deviation.

## Overview
**Files:**
- `scripts/populateTestData.ts` (Medium)
- `scripts/dumpDatabaseContents.ts` (Low)
- `scripts/populateTimerTestData.ts` (Medium)

**Complexity:** MEDIUM - Standalone scripts with direct database usage

## Tasks

### 6.1 populateTestData.ts
Replace sqlite3 import and Database type.

**Replace callback flow with synchronous:**
- const db = new Database(dbPath)
- db.pragma('foreign_keys = ON');
- Create schema via db.exec() if resetting DB. If using existing, skip schema creation.

**Replace createFolder/createNote to sync:**
```typescript
const stmt = db.prepare('INSERT ...');
const info = stmt.run(...);
return { id: Number(info.lastInsertRowid), name/title };
```

**Implementation steps:**
- Iterate and insert without Promises. Keep logging.
- Finalize with db.close() and log close.
- Maintain CLI arguments and existing behavior.

### 6.2 dumpDatabaseContents.ts
- Replace reading logic with db.prepare(...).all() and console.log outputs.
- Remove async/await patterns and callback handling.
- Maintain existing output format and structure.
- Ensure proper database connection and cleanup.

### 6.3 populateTimerTestData.ts
- Mirror populateTestData.ts approach for timer-related schema and inserts.
- Replace sqlite3 initialization with better-sqlite3 synchronous pattern.
- Update all INSERT operations to use prepared statements.
- Maintain existing test data structure and relationships.

### 6.4 dumpDatabaseContents.cjs (CommonJS version)
- Update CommonJS require statements for better-sqlite3.
- Convert callback-based database operations to synchronous.
- Maintain compatibility with CommonJS module system.
- Ensure output format matches TypeScript version.

## Common Patterns for All Scripts

### Database Initialization
```typescript
// Old pattern
const db = new sqlite3.Database(dbPath, (err) => {
  if (err) {
    console.error('Error opening database:', err.message);
    return;
  }
  // ... async operations
});

// New pattern
const db = new Database(dbPath);
db.pragma('foreign_keys = ON');
// ... synchronous operations
db.close();
```

### Error Handling
```typescript
// Wrap operations in try/catch
try {
  const db = new Database(dbPath);
  // ... database operations
  db.close();
  console.log('Script completed successfully');
} catch (error) {
  console.error('Script failed:', error.message);
  process.exit(1);
}
```

### Logging and Progress
- Maintain existing console.log statements for progress tracking.
- Keep timing information if present.
- Preserve any CLI argument handling.

## Dependencies
- Phase 5 must be completed (all API modules migrated)

## Prerequisites
- better-sqlite3 installed and working
- Understanding of current script functionality and CLI interfaces

## Deliverables
- Scripts are synchronous and simpler, maintain CLI arguments and logging semantics.
- All database operations converted to better-sqlite3 patterns.
- Proper error handling and cleanup implemented.
- No changes to script interfaces or expected outputs.
- All scripts tested and verified working.
