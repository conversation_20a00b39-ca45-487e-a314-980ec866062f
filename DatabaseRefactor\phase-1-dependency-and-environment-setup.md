# Phase 1: Dependency and Environment Setup

**AI Agent Instructions**: Use extended thinking for all decisions. Meticulously review this plan before execution. Always read and analyze files thoroughly before making any modifications. Follow this plan step-by-step without deviation.

## Overview
**Goal**: Remove sqlite3, add better-sqlite3, ensure Electron build compatibility.

## Tasks

### 1.1 Dependencies Management
**Remove existing sqlite3 dependencies:**
```bash
npm uninstall sqlite3
npm uninstall @types/sqlite3  # If present in devDependencies
```

**Add better-sqlite3:**
```bash
npm install better-sqlite3
```
- Types are bundled with better-sqlite3; @types package not required
- Verify TypeScript usage works with built-in definitions

### 1.2 Package.json Script Updates ⚠️ **CRITICAL ADDITION**
**Update build and rebuild scripts** (addresses identified gap):

**Current scripts that need updating:**
```json
{
  "scripts": {
    "postinstall": "electron-rebuild -f -w sqlite3",
    "electron-rebuild": "electron-rebuild -f -w sqlite3"
  }
}
```

**Updated scripts:**
```json
{
  "scripts": {
    "postinstall": "electron-rebuild -f -w better-sqlite3",
    "electron-rebuild": "electron-rebuild -f -w better-sqlite3"
  }
}
```

**Rationale**: Electron-rebuild needs to compile native modules for the Electron runtime. Failing to update these scripts will cause build failures.

### 1.3 Electron Build Verification
**Ensure electron-builder compatibility:**
- Verify electron-builder bundles native better-sqlite3 binaries correctly
- For Windows: Ensure Node.js native tools are installed if compilation required
- Test installation: `node -e "console.log(require('better-sqlite3'))"`

**Build verification steps:**
1. Clean install: `rm -rf node_modules package-lock.json && npm install`
2. Rebuild native modules: `npm run electron-rebuild`
3. Test import: `node -e "const db = require('better-sqlite3')(':memory:'); console.log('Success')"`
4. Build application: `npm run build`

### 1.4 Cross-platform Considerations
**Primary platform**: Windows 11 (verified working)
**Secondary platforms**: macOS, Linux (optional verification)

**Special considerations:**
- If custom SQLite build needed (rare), consider build-from-source flags
- Review `nativeBinding` option if standard binaries don't work
- Test on target deployment platforms early in migration

### 1.5 Development Environment Setup
**Recommended development workflow:**
1. Create feature branch: `git checkout -b feature/better-sqlite3-migration`
2. Tag current state: `git tag sqlite3-baseline`
3. Set up parallel testing environment
4. Configure IDE for better-sqlite3 TypeScript definitions

## Dependencies
- Phase 0 must be completed (baseline established)

## Prerequisites
- Node.js and npm installed
- Electron development environment configured
- Git repository in clean state

## Deliverables
- sqlite3 dependencies removed
- better-sqlite3 installed and verified
- Package.json scripts updated for electron-rebuild
- Build verification completed successfully
- Development environment configured for migration
