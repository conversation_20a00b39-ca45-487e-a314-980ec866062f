# Phase 3: Database-API Layer Rewrite

**AI Agent Instructions**: Use extended thinking for all decisions. Meticulously review this plan before execution. Always read and analyze files thoroughly before making any modifications. Follow this plan step-by-step without deviation.

## Overview
**File:** `electron/main/database/database-api.ts` (CRITICAL)

**Complexity:** HIGH - Central database abstraction layer used by entire application

**Impact:** All API modules depend on this layer - changes affect entire codebase

## Tasks

### 3.1 Imports and Types

**Current imports:**
```typescript
import { getDatabase } from './database';
import sqlite3 from 'sqlite3';
import * as fs from 'fs';

// Types
type Database = sqlite3.Database;
type RunResult = sqlite3.RunResult;
```

**New imports:**
```typescript
import { getDatabase } from './database';
import type Database from 'better-sqlite3';
import * as fs from 'fs';

// Types - better-sqlite3 provides these built-in
// No need for separate RunResult type - use return type directly
```

**Type changes:**
- Remove `sqlite3.Database` and `sqlite3.RunResult` references
- Use better-sqlite3's built-in TypeScript definitions
- Update function signatures to match new return types

### 3.2 Replace helper functions dbGet/dbAll/dbRun
Old async Promise-based helpers wrap callbacks. Replace with sync:

```typescript
const dbGet = <T>(query: string, params: any[] = []): T | undefined => {
  const db = getDatabase();
  const stmt = db.prepare(query);
  return stmt.get(...params) as T | undefined;
};

const dbAll = <T>(query: string, params: any[] = []): T[] => {
  const db = getDatabase();
  const stmt = db.prepare(query);
  return stmt.all(...params) as T[];
};

const dbRun = (query: string, params: any[] = []): { changes: number; lastInsertRowid: number } => {
  const db = getDatabase();
  const stmt = db.prepare(query);
  const info = stmt.run(...params);
  return { changes: info.changes, lastInsertRowid: Number(info.lastInsertRowid) };
};
```

If external modules expect RunResult.lastID, provide a compatibility wrapper:
```typescript
type RunInfoCompat = { changes: number; lastID: number };
const dbRunCompat = (...) => {
  const info = stmt.run(...params);
  return { changes: info.changes, lastID: Number(info.lastInsertRowid) };
};
```
Update all consumers in this file to use new return shape (prefer migrating to lastInsertRowid; if keeping .lastID internally, translate immediately after run()).

### 3.3 Update CRUD functions to sync pattern
- createNote/createFolder/createBook: 
  - After INSERT, retrieve new row via get by lastInsertRowid.
- get...(): directly return dbGet/dbAll result. Add explicit not-found checks when prior code expected throws.
- update...(): run UPDATE, check changes, then fetch updated entity.
- delete...(): run DELETE, return success based on changes > 0.

### 3.4 Transactions
Rewrite withTransaction and withReadTransaction using db.transaction():
```typescript
const withTransaction = <T>(operation: () => T): T => {
  const db = getDatabase();
  const tx = db.transaction(operation);
  return tx();
};
```

For read transaction semantics (consistent snapshot), consider tx = db.transaction(operation) and call tx.immediate() or deferred() depending on previous behavior (BEGIN IMMEDIATE TRANSACTION previously used in withReadTransaction). Mirror with .immediate() for close fidelity:
```typescript
const withReadTransaction = <T>(operation: () => T): T => {
  const db = getDatabase();
  return db.transaction(operation).immediate();
};
```

### 3.5 deleteBook special case
Old version used db.serialize(), BEGIN/COMMIT, file deletion, and multiple queries. Convert to:
- Use db.transaction((id: number) => { ... }) returning { success, id }.
- Inside transaction:
  - Query media files for the book (SELECT ...).
  - Delete DB records for media (DELETE ...).
  - Defer filesystem deletions:
    - Best practice: either perform fs.unlinkSync inside transaction cautiously (risk if unlink throws).
    - Safer: gather file paths inside transaction, perform fs operations after transaction successfully commits; if unlink fails, log warnings.
  - Delete book row, then commit by returning.
- If the old code relied on ON DELETE SET NULL for notes.book_id, keep behavior.

### 3.6 Statement caching (optional optimization)
Implement a small cache for hot queries:
```typescript
const cache = new Map<string, Database.Statement>();
function prepareCached(sql: string) {
  if (!cache.has(sql)) cache.set(sql, getDatabase().prepare(sql));
  return cache.get(sql)!;
}
```

Use cache for frequent SELECTs in list retrieval or hot paths.

## Dependencies
- Phase 2 must be completed (core database layer migrated)

## Prerequisites
- database.ts fully migrated to better-sqlite3
- Understanding of current database-api.ts structure and usage patterns

## Deliverables
- database-api.ts fully synchronous, with replaced helpers, transactions via db.transaction, and compatibility handling for lastInsertRowid.
- All CRUD functions updated to synchronous patterns
- Transaction helpers rewritten using better-sqlite3 transaction API
- Optional statement caching implemented for performance
