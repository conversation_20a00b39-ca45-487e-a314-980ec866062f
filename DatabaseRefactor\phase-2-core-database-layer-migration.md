# Phase 2: Core Database Layer Migration

**AI Agent Instructions**: Use extended thinking for all decisions. Meticulously review this plan before execution. Always read and analyze files thoroughly before making any modifications. Follow this plan step-by-step without deviation.

## Overview
**Files:**
- `electron/main/database/database.ts` (CRITICAL)
- `electron/main/database/database-hooks.ts` (integration review required)

**Complexity**: HIGH - Core infrastructure changes affecting entire application

## Tasks

### 2.1 Replace sqlite3 Import and Types

**Current implementation:**
```typescript
import sqlite3 from 'sqlite3';
import { app } from 'electron';
// ...
type Database = sqlite3.Database;
```

**New implementation:**
```typescript
import Database from 'better-sqlite3';
import { app } from 'electron';
// ...
type DatabaseInstance = Database;  // Database is constructor, instances have this type
```

**Key differences:**
- `Database` is now the constructor function, not a type
- Instance type is the return type of `new Database()`
- No need for separate type imports

### 2.2 Connection Lifecycle and Singleton

**Current callback-based initialization:**
```typescript
const db: Database = new sqlite3.Database(dbPath, async (err: Error | null) => {
  if (err) {
    console.error('Error opening database:', err.message);
    reject(err);
    return;
  }
  // ... async setup
});
```

**New synchronous initialization:**
```typescript
const db = new Database(dbPath, {
  readonly: false,
  fileMustExist: false,
  timeout: 10000,
  verbose: process.env.NODE_ENV === 'development' ? console.log : null
});
```

**Updated singleton pattern:**
```typescript
let dbInstance: DatabaseInstance | null = null;

export const getDatabase = (): DatabaseInstance => {
  if (!dbInstance) {
    dbInstance = initDatabase(); // Now synchronous
  }
  return dbInstance;
};

export const closeDatabase = (): void => {
  if (dbInstance) {
    dbInstance.close();
    dbInstance = null;
  }
};
```

**⚠️ Critical Addition - getDatabase() Fallback Path:**
The current `getDatabase()` function has a fallback creation path that also needs migration:
```typescript
// Current fallback (lines 469-477 in database.ts)
export const getDatabase = (): Database => {
  if (dbInstance) {
    return dbInstance;
  }

  const dbPath = getDbPath();
  dbInstance = new sqlite3.Database(dbPath, (err: Error | null) => {
    if (err) {
      console.error('Error connecting to database:', err.message);
      throw err;
    }
  });

  return dbInstance;
};
```

**Updated fallback:**
```typescript
export const getDatabase = (): DatabaseInstance => {
  if (dbInstance) {
    return dbInstance;
  }

  const dbPath = getDbPath();
  dbInstance = new Database(dbPath, {
    readonly: false,
    fileMustExist: true, // Database should exist by this point
    timeout: 10000
  });

  return dbInstance;
};
```

### 2.3 Initialization Flow Becomes Synchronous
- Old setup functions use runAsync/getAsync helpers and await.
- New setup should use db.exec() and db.pragma(). No async/await needed unless preserving API shape.

Suggested structure:
- getUserDataPath()/ensureDataDirectory()/getDbPath() unchanged.
- setupDatabaseConfig(db):
  - db.pragma('busy_timeout = 10000');  // or use Database option timeout
  - db.pragma('journal_mode = WAL');
  - db.pragma('foreign_keys = ON');
- createAllTables(db):
  - Use db.exec(multi-statement SQL) or sequential db.exec() per table. Keep same schema.
- handleDatabaseMigrations(db):
  - Use try/catch blocks around db.exec('ALTER TABLE ...') similar to previous, but synchronous.
  - Note: SQLite ALTER DROP COLUMN isn't supported in older versions. The old code attempted DROP COLUMN and tolerated failures; keep the logic but expect exceptions. If DROP COLUMN is not supported, silently continue as previous code does.
- createDatabaseIndexes(db):
  - db.exec('CREATE INDEX IF NOT EXISTS ...');
- setupDefaultData(db):
  - Use prepare().get() and prepare().run() for SELECT/INSERT.

### 2.4 Critical Pragma and Transaction Sequencing

**⚠️ CRITICAL**: Execute pragmas BEFORE transactions (pragmas cannot run inside transactions):

```typescript
const initDatabase = (): DatabaseInstance => {
  const db = new Database(dbPath, options);

  // Execute pragmas FIRST, outside any transaction
  db.pragma('busy_timeout = 10000');
  db.pragma('journal_mode = WAL');
  db.pragma('foreign_keys = ON');

  // Then wrap schema operations in transaction
  const setup = db.transaction(() => {
    createAllTables(db);
    handleDatabaseMigrations(db);
    createDatabaseIndexes(db);
    setupDefaultData(db);
  });
  setup();

  return db;
};
```

**Why this sequence matters:**
1. **Pragmas must be outside transactions** - SQLite restriction
2. **Schema operations benefit from transactions** - Atomic setup
3. **WAL mode enables better concurrency** - Must be set early
4. **Foreign keys must be enabled** - Before any data operations

### 2.5 Remove Async Helpers

**Functions to remove:**
```typescript
// Remove these helper functions (lines 12-29 in database.ts)
const runAsync = (db: Database, sql: string): Promise<void> => { /* ... */ };
const getAsync = (db: Database, sql: string): Promise<any> => { /* ... */ };
```

**Replacement pattern:**
```typescript
// Old: await runAsync(db, 'CREATE TABLE ...')
// New: db.exec('CREATE TABLE ...')

// Old: await getAsync(db, 'SELECT ...')
// New: db.prepare('SELECT ...').get()
```

### 2.6 Error Handling, Logging, and Hooks

**Maintain initialization logs:**
```typescript
console.log(`Initializing database at: ${dbPath}`);
console.log('Connected to the SQLite database.');
console.log('WAL mode enabled successfully');
console.log('Foreign key support enabled.');
```

**Database hooks compatibility:**
- `databaseHooks.initialize()` and `databaseHooks.shutdown()` calls remain unchanged
- Hooks system is event-based and doesn't assume callback-style DB access
- Verify hooks don't expect async database operations

**Error handling patterns:**
```typescript
// Maintain existing error handling structure
try {
  setupDatabase(db);
  dbInstance = db;
  console.log('Database singleton instance assigned.');
} catch (setupError) {
  console.error('Error setting up database:', setupError);
  throw setupError;
}
```

### 2.7 PRAGMA Compatibility

**Use better-sqlite3 pragma method:**
```typescript
// Old: await runAsync(db, 'PRAGMA journal_mode = WAL');
// New: db.pragma('journal_mode = WAL');

// Return value handling (optional)
const walMode = db.pragma('journal_mode = WAL');
console.log('WAL mode result:', walMode); // Returns 'wal' if successful
```

**Supported pragma calls:**
- `db.pragma('busy_timeout = 10000')`
- `db.pragma('journal_mode = WAL')`
- `db.pragma('foreign_keys = ON')`
- `db.pragma('cache_size = 32000')` (optimization)

## Dependencies
- Phase 1 must be completed (better-sqlite3 installed)

## Prerequisites
- better-sqlite3 dependency installed and verified
- Understanding of current database initialization flow

## Deliverables
- database.ts fully migrated to synchronous initialization, singleton connection, pragmas, schema creation, migrations, default data and indexes inside a single transaction.
- No external API change for getDatabase() if possible (but now returns BetterDatabase).
- All async helper functions removed
- Proper error handling and logging maintained
