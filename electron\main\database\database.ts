// Database utility for SQLite connection & initialization
import Database from 'better-sqlite3';
import { app } from 'electron';
import * as path from 'node:path';
import * as fs from 'node:fs';
import { databaseHooks } from './database-hooks';

// Type for Database
type DatabaseInstance = Database;

// Note: Removed runAsync/getAsync helper functions as they were misleading
// (named as async but were synchronous). Now using db.exec() and db.prepare().get() directly.

// Database will be stored in the user's application data directory
const getUserDataPath = () => {
    return app.getPath('userData');
};

// Ensure data directory exists
const ensureDataDirectory = () => {
    const userDataPath = getUserDataPath();
    if (!fs.existsSync(userDataPath)) {
        fs.mkdirSync(userDataPath, { recursive: true });
    }
    return userDataPath;
};

// Get database path
const getDbPath = () => {
    const userDataPath = ensureDataDirectory();
    return path.join(userDataPath, 'noti-database.sqlite');
};

// Setup database configuration (pragmas)
const setupDatabaseConfig = (db: DatabaseInstance): void => {
    try {
        // Set busy timeout first to handle potential locks during initialization
        db.pragma('busy_timeout = 10000');

        // Enable WAL mode immediately after setting timeout
        const walMode = db.pragma('journal_mode = WAL');
        console.log('WAL mode enabled successfully, result:', walMode);

        // Enable foreign keys
        db.pragma('foreign_keys = ON');
        console.log('Foreign key support enabled.');

        // Performance optimization pragmas
        // Set explicit page size (SQLite default is 4KB since 3.12.0)
        db.pragma('page_size = 4096');
        console.log('Page size set to 4KB');

        // Cache size: 32MB using KiB-based setting (negative value = KiB, positive = pages)
        db.pragma('cache_size = -32768'); // -32768 KiB = 32MB
        console.log('Cache size set to 32MB (32768 KiB)');

        // Synchronous mode: NORMAL for balance between safety and performance
        // NORMAL is safe with WAL mode and provides good performance
        db.pragma('synchronous = NORMAL');
        console.log('Synchronous mode set to NORMAL');

        // Store temporary tables and indices in memory for better performance
        db.pragma('temp_store = MEMORY');
        console.log('Temporary storage set to MEMORY');

        // Memory-mapped I/O: 256MB for better I/O performance
        db.pragma('mmap_size = 268435456');
        console.log('Memory-mapped I/O size set to 256MB');

    } catch (error) {
        if (error instanceof Error) {
            if (error.message.includes('busy_timeout')) {
                console.error('Error setting busy timeout:', error.message);
            } else if (error.message.includes('journal_mode')) {
                console.error('Error enabling WAL mode:', error.message);
            } else if (error.message.includes('foreign_keys')) {
                console.error('Error enabling foreign keys:', error.message);
            } else if (error.message.includes('cache_size')) {
                console.error('Error setting cache size:', error.message);
            } else if (error.message.includes('synchronous')) {
                console.error('Error setting synchronous mode:', error.message);
            } else if (error.message.includes('temp_store')) {
                console.error('Error setting temp store:', error.message);
            } else if (error.message.includes('mmap_size')) {
                console.error('Error setting mmap size:', error.message);
            } else {
                console.error('Error in database configuration:', error.message);
            }
        }
        throw error;
    }
};

// Create all database tables
const createAllTables = (db: DatabaseInstance): void => {
    try {
        // Create Notes table - Note that "order" is a reserved keyword, so we escape it with quotes
        db.exec(`CREATE TABLE IF NOT EXISTS notes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            content TEXT,
            html_content TEXT,
            folder_id INTEGER,
            book_id INTEGER,
            type TEXT,
            color TEXT,
            "order" INTEGER,
            last_viewed_at TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE SET NULL,
            FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE SET NULL
        )`);

        // Create Folders table - Note that "order" is a reserved keyword, so we escape it with quotes
        db.exec(`CREATE TABLE IF NOT EXISTS folders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            parent_id INTEGER,
            book_id INTEGER,
            color TEXT,
            "order" INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (parent_id) REFERENCES folders(id) ON DELETE CASCADE,
            FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE SET NULL
        )`);

        // Create Books table
        db.exec(`CREATE TABLE IF NOT EXISTS books (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            author TEXT,
            isbn TEXT,
            cover_url TEXT,
            publication_date TEXT,
            description TEXT,
            page_count INTEGER,
            current_page INTEGER,
            rating INTEGER,
            language TEXT,
            genres TEXT,
            olid TEXT,
            status TEXT,
            custom_fields TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`);

        // Create Recent Items table
        db.exec(`CREATE TABLE IF NOT EXISTS recent_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            note_id INTEGER,
            book_id INTEGER,
            viewed_at TIMESTAMP NOT NULL,
            FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
            FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
        )`);

        // Create Theme Settings table
        db.exec(`CREATE TABLE IF NOT EXISTS theme_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            theme_name TEXT NOT NULL,
            is_active BOOLEAN NOT NULL DEFAULT 0,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`);

        // Create Exports table
        db.exec(`CREATE TABLE IF NOT EXISTS exports (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            note_id INTEGER NOT NULL,
            export_path TEXT NOT NULL,
            export_type TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE
        )`);

        // Create Search History table
        db.exec(`CREATE TABLE IF NOT EXISTS search_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            query TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`);

        // Create Settings table
        db.exec(`CREATE TABLE IF NOT EXISTS settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            key TEXT NOT NULL UNIQUE,
            value_json TEXT,
            category TEXT,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`);

        // Create Media Files table
        db.exec(`CREATE TABLE IF NOT EXISTS media_files (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            note_id INTEGER,
            book_id INTEGER,
            file_path TEXT NOT NULL,
            file_name TEXT NOT NULL,
            file_type TEXT,
            file_size INTEGER,
            is_cover BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (note_id) REFERENCES notes(id) ON DELETE CASCADE,
            FOREIGN KEY (book_id) REFERENCES books(id) ON DELETE CASCADE
        )`);

        // Create Timer Sessions table
        db.exec(`CREATE TABLE IF NOT EXISTS timer_sessions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            start_time TIMESTAMP NOT NULL,
            end_time TIMESTAMP,
            duration INTEGER,
            session_type TEXT,
            is_completed BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            focus TEXT,
            category TEXT,
            updated_at TIMESTAMP,
            session_name TEXT,
            pomodoro_cycles_completed INTEGER DEFAULT 0,
            is_user_session BOOLEAN DEFAULT 1
        )`);

        // Create Pomodoro Cycles table
        db.exec(`CREATE TABLE IF NOT EXISTS pomodoro_cycles (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            session_id INTEGER NOT NULL,
            cycle_type TEXT NOT NULL CHECK (cycle_type IN ('pomodoro', 'short_break', 'long_break')),
            start_time TIMESTAMP NOT NULL,
            end_time TIMESTAMP,
            duration INTEGER,
            completed BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (session_id) REFERENCES timer_sessions(id) ON DELETE CASCADE
        )`);

        // Create Timer Settings table
        db.exec(`CREATE TABLE IF NOT EXISTS timer_settings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            work_duration INTEGER NOT NULL DEFAULT 1500,
            short_break_duration INTEGER NOT NULL DEFAULT 300,
            long_break_duration INTEGER NOT NULL DEFAULT 900,
            long_break_interval INTEGER NOT NULL DEFAULT 4,
            auto_start_breaks BOOLEAN DEFAULT 1,
            auto_start_work BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`);

        // Create sync_state table for tracking sync status
        db.exec(`CREATE TABLE IF NOT EXISTS sync_state (
            item_type TEXT NOT NULL,
            item_id INTEGER NOT NULL,
            sync_hash TEXT NOT NULL,
            last_synced TIMESTAMP NOT NULL,
            device_id TEXT NOT NULL,
            sync_version INTEGER DEFAULT 1,
            PRIMARY KEY (item_type, item_id)
        )`);

        // Create sync_sessions table for tracking sync operations
        db.exec(`CREATE TABLE IF NOT EXISTS sync_sessions (
            id TEXT PRIMARY KEY,
            device_id TEXT NOT NULL,
            started_at TIMESTAMP NOT NULL,
            completed_at TIMESTAMP,
            status TEXT NOT NULL CHECK (status IN ('in_progress', 'completed', 'failed')),
            items_synced INTEGER DEFAULT 0,
            error_message TEXT
        )`);

        // Create sync_directory_state table for unified sync engine
        db.exec(`CREATE TABLE IF NOT EXISTS sync_directory_state (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            directory TEXT UNIQUE NOT NULL,
            last_sync_hash TEXT,
            last_sync_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`);

        // Phase 3: Removed sync_items table - sync state now tracked in manifest only

    } catch (error) {
        console.error('Error creating database tables:', error);
        throw error;
    }
};

// Handle database migrations (add columns that might not exist)
const handleDatabaseMigrations = (db: DatabaseInstance): void => {
    try {
        // Remove cover_data and cover_path columns from existing books table if they exist
        // Note: ALTER TABLE DROP COLUMN requires SQLite ≥ 3.35.0 (March 2021)
        try {
            db.exec(`ALTER TABLE books DROP COLUMN cover_data`);
        } catch (alterErr) {
            if (alterErr instanceof Error) {
                // Handle both "no such column" and syntax errors from older SQLite versions
                if (alterErr.message.includes('no such column')) {
                    // Column doesn't exist, which is fine - silently continue
                } else if (alterErr.message.includes('syntax error') ||
                          alterErr.message.includes('DROP COLUMN') ||
                          alterErr.message.includes('near "DROP"')) {
                    console.warn('SQLite version does not support DROP COLUMN, skipping cover_data column removal');
                } else {
                    console.error('Warning: Failed to drop cover_data column:', alterErr.message);
                }
            }
        }

        try {
            db.exec(`ALTER TABLE books DROP COLUMN cover_path`);
        } catch (alterErr) {
            if (alterErr instanceof Error) {
                // Handle both "no such column" and syntax errors from older SQLite versions
                if (alterErr.message.includes('no such column')) {
                    // Column doesn't exist, which is fine - silently continue
                } else if (alterErr.message.includes('syntax error') ||
                          alterErr.message.includes('DROP COLUMN') ||
                          alterErr.message.includes('near "DROP"')) {
                    console.warn('SQLite version does not support DROP COLUMN, skipping cover_path column removal');
                } else {
                    console.error('Warning: Failed to drop cover_path column:', alterErr.message);
                }
            }
        }

        // Migrate existing media_files table to add book_id and is_cover columns if they don't exist
        try {
            db.exec(`ALTER TABLE media_files ADD COLUMN book_id INTEGER`);
        } catch (alterErr) {
            // Ignore error if column already exists
            if (alterErr instanceof Error && !alterErr.message.includes('duplicate column name')) {
                console.warn('Note: Could not add book_id column:', alterErr.message);
            }
        }

        try {
            db.exec(`ALTER TABLE media_files ADD COLUMN is_cover BOOLEAN DEFAULT 0`);
        } catch (alterErr) {
            // Ignore error if column already exists
            if (alterErr instanceof Error && !alterErr.message.includes('duplicate column name')) {
                console.warn('Note: Could not add is_cover column:', alterErr.message);
            }
        }

        // Migrate existing folders table to add book_id column if it doesn't exist
        try {
            db.exec(`ALTER TABLE folders ADD COLUMN book_id INTEGER`);
        } catch (alterErr) {
            // Ignore error if column already exists
            if (alterErr instanceof Error && !alterErr.message.includes('duplicate column name')) {
                console.warn('Note: Could not add book_id column to folders:', alterErr.message);
            }
        }

        // Add created_at column to existing timer_settings table if it doesn't exist
        try {
            db.exec(`ALTER TABLE timer_settings ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`);
        } catch (alterErr) {
            if (alterErr instanceof Error && !alterErr.message.includes('duplicate column name')) {
                console.warn('Warning: Could not add created_at column to timer_settings:', alterErr.message);
            } else if (!alterErr) {
                console.log('Column "created_at" added to timer_settings or already exists.');
            }
        }

    } catch (error) {
        console.error('Error handling database migrations:', error);
        throw error;
    }
};

// Create database indexes for performance
const createDatabaseIndexes = (db: DatabaseInstance): void => {
    const indexes = [
        { sql: 'CREATE INDEX IF NOT EXISTS idx_notes_folder_id ON notes (folder_id)', name: 'notes.folder_id' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_folders_parent_id ON folders (parent_id)', name: 'folders.parent_id' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_timer_sessions_start_time ON timer_sessions (start_time DESC)', name: 'timer_sessions_start_time' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_timer_sessions_session_type ON timer_sessions (session_type)', name: 'timer_sessions_session_type' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_timer_sessions_category ON timer_sessions (category)', name: 'timer_sessions_category' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_timer_sessions_is_completed ON timer_sessions (is_completed)', name: 'timer_sessions_is_completed' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_timer_sessions_start_completed ON timer_sessions (start_time DESC, is_completed)', name: 'timer_sessions_start_completed' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_timer_sessions_is_user_session ON timer_sessions (is_user_session)', name: 'timer_sessions_is_user_session' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_pomodoro_cycles_session_id ON pomodoro_cycles (session_id)', name: 'pomodoro_cycles_session_id' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_pomodoro_cycles_cycle_type ON pomodoro_cycles (cycle_type)', name: 'pomodoro_cycles_cycle_type' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_notes_updated_at ON notes(updated_at)', name: 'notes_updated_at' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_folders_updated_at ON folders(updated_at)', name: 'folders_updated_at' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_sync_state_last_synced ON sync_state(last_synced)', name: 'sync_state_last_synced' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_sync_sessions_device_id ON sync_sessions(device_id)', name: 'sync_sessions_device_id' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_sync_sessions_status ON sync_sessions(status)', name: 'sync_sessions_status' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_sync_directory_state_directory ON sync_directory_state(directory)', name: 'sync_directory_state_directory' },
        // Missing critical indexes identified in database review
        { sql: 'CREATE INDEX IF NOT EXISTS idx_notes_book_id ON notes(book_id)', name: 'notes_book_id' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_media_files_book_cover ON media_files(book_id, is_cover)', name: 'media_files_book_cover' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_books_title ON books(title)', name: 'books_title' },
        { sql: 'CREATE INDEX IF NOT EXISTS idx_books_author ON books(author)', name: 'books_author' },
        // Data integrity constraint: enforce one-to-one book-folder relationship
        { sql: 'CREATE UNIQUE INDEX IF NOT EXISTS idx_folders_book_unique ON folders(book_id) WHERE book_id IS NOT NULL', name: 'folders_book_unique' }
    ];

    for (const index of indexes) {
        try {
            db.exec(index.sql);
        } catch (indexErr) {
            console.error(`Error creating index ${index.name}:`, indexErr);
            // Not rejecting, as this is an optimization, but logging the error
        }
    }
};

// Create default folders and setup
const setupDefaultData = (db: DatabaseInstance): void => {
    try {
        // Create unique constraint for Books root folder to prevent duplicates
        try {
            db.exec(`CREATE UNIQUE INDEX IF NOT EXISTS idx_books_root_folder
                ON folders (name, parent_id)
                WHERE name = 'Books' AND parent_id IS NULL`);
        } catch (constraintErr) {
            console.error('Error creating Books folder unique constraint:', constraintErr);
            // Continue anyway, the INSERT will still work
        }

        // Check if Books folder already exists before creating
        const existingBooksFolder = db.prepare(`SELECT id FROM folders WHERE name = 'Books' AND parent_id IS NULL LIMIT 1`).get();

        if (existingBooksFolder) {
            console.log('Books folder already exists with ID:', existingBooksFolder.id);
        } else {
            // Create the default "Books" root folder
            db.exec(`INSERT INTO folders (name, parent_id, color, created_at, updated_at)
                VALUES ('Books', NULL, '#4285F4', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`);
            console.log('Default "Books" folder created successfully.');
        }

    } catch (error) {
        console.error('Error setting up default data:', error);
        throw error;
    }
};

// Main function to handle all database setup after connection
const setupDatabase = (db: DatabaseInstance): void => {
    try {
        // First, set up database configuration (pragmas) - these must be done outside transactions
        setupDatabaseConfig(db);

        // Wrap schema setup operations in a single transaction for atomicity
        const setupTransaction = db.transaction(() => {
            createAllTables(db);
            handleDatabaseMigrations(db);
            createDatabaseIndexes(db);
            setupDefaultData(db);
        });

        // Execute the transaction
        setupTransaction();

        console.log('All database tables and indexes created successfully.');

        // Initialize database hooks manager
        databaseHooks.initialize();
        console.log('Database hooks manager initialized.');

    } catch (error) {
        console.error('Error during database setup:', error);
        throw error;
    }
};

// Initialize the database
// Initialize the database connection and setup all tables/indexes
export const initDatabase = (): DatabaseInstance => {
    try {
        const dbPath = getDbPath();
        console.log(`Initializing database at: ${dbPath}`);
        
        // Create new database connection
        const db = new Database(dbPath);
        
        // Run all setup functions
        setupDatabase(db);
        
        // Assign to singleton instance
        dbInstance = db;
        
        console.log('Database initialized successfully');
        return db;
    } catch (error) {
        console.error('Failed to initialize database:', error);
        throw error;
    }
};

// Singleton database connection
let dbInstance: Database | null = null;

// Get database connection
export const getDatabase = (): DatabaseInstance => {
    if (dbInstance) {
        return dbInstance;
    }

    // If no instance exists, initialize the database properly to ensure schema setup
    console.log('Database instance not found, initializing database...');
    return initDatabase();
};

// Close database connection (call this when app is shutting down)
export const closeDatabase = (): void => {
    if (dbInstance) {
        // Shutdown database hooks manager first
        databaseHooks.shutdown();
        
        dbInstance.close();
        console.log('Database connection closed.');
        dbInstance = null;
    }
};
