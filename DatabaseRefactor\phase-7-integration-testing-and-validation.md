# Phase 7: Integration, Testing, and Validation

**AI Agent Instructions**: Use extended thinking for all decisions. Meticulously review this plan before execution. Always read and analyze files thoroughly before making any modifications. Follow this plan step-by-step without deviation.

## Overview
**Goal:** Comprehensive testing and validation of the complete migration

**Complexity:** HIGH - Critical validation phase ensuring system stability

## Tasks

### 7.1 Build and run
- Rebuild the app. Ensure better-sqlite3 native module loads under Electron runtime.
- Launch Noti; verify database initialization logs and WAL mode activation.
- Check for any runtime errors or warnings in console.
- Verify application starts successfully and database connection is established.

### 7.2 Functional testing
**Database initialization on fresh profile:**
- Tables created correctly
- Default data created
- Indexes properly established
- Foreign key constraints working

**CRUD operations for notes, folders, books:**
- Create new items and verify they appear correctly
- Update existing items and confirm changes persist
- Delete items and ensure proper cleanup
- Test bulk operations and data integrity

**Timer flows:**
- Start/End user session functionality
- Start/complete pomodoro cycles
- Stats calculation accuracy
- Settings changes persistence
- Session data consistency

**Recent items and media relations:**
- Recent items tracking works correctly
- Media file associations maintained
- Cross-references between entities preserved

### 7.3 Transaction integrity
- Force errors inside transaction to confirm automatic rollback by db.transaction (throwing should rollback).
- End-to-end multi-step operations retain atomicity.
- Test complex operations like book deletion with media cleanup.
- Verify foreign key constraint enforcement.

### 7.4 Performance benchmarking
Use rough timings to compare:
- Insertion of N notes/folders (batched within a transaction).
- Bulk SELECT .all() with varying cardinalities.
- Complex JOIN operations and aggregations.
- Timer statistics calculations.
- Expect major improvements vs sqlite3.

**Specific benchmarks to run:**
- App startup time (database initialization)
- Note creation (single and batch)
- Search operations across large datasets
- Timer session operations
- Sync manifest generation

### 7.5 Cross-platform validation
- **Windows 11 (primary):** Comprehensive testing
- **Optional:** macOS, Linux packaging tests
- Verify native module compilation on different platforms
- Test Electron packaging and distribution

### 7.6 WAL checkpoint policy (optional)
Consider periodic checkpoint to avoid WAL growth if app has many long-lived readers:
- Implement optional interval to monitor db-wal size
- Run db.pragma('wal_checkpoint(RESTART)') if exceeds threshold
- Test checkpoint behavior under load

## Testing Strategy

### Unit Tests
- **Update existing tests** for database-api and timer-api critical paths
- **Add new tests** for better-sqlite3 specific functionality
- **Test transaction behavior** - ensure rollback works correctly
- **Test prepared statement caching** if implemented
- **Verify error handling** matches previous behavior

### Integration Tests
- **End-to-end sanity:** Launch app, create/update/delete notes, folders, and books
- **Timer functionality:** Create sessions, pomodoro completion, validate counters and stats
- **Sync operations:** Full sync cycle testing with new database layer
- **Settings management:** Verify all settings operations work correctly

### Performance Testing
- **Benchmark critical operations** before/after migration
- **Log performance improvements** for documentation
- **Test with large datasets** to verify scalability improvements
- **Monitor memory usage** during extended operations

## Troubleshooting Guide

### Common Migration Issues

**Issue: "Cannot find module 'better-sqlite3'"**
- Solution: Ensure `npm install better-sqlite3` completed successfully
- Check: Verify native module compilation with `npm run electron-rebuild`

**Issue: "Database file is locked"**
- Solution: Ensure all sqlite3 connections are properly closed before migration
- Check: Look for lingering database connections in development tools

**Issue: "PRAGMA statements fail"**
- Solution: Verify pragmas are executed outside of transactions
- Check: Ensure pragma syntax matches better-sqlite3 format

**Issue: "Transaction rollback not working"**
- Solution: Ensure errors are thrown (not returned) within transaction functions
- Check: Verify transaction function structure matches better-sqlite3 patterns

**Issue: "Performance not improved"**
- Solution: Verify prepared statements are being used correctly
- Check: Implement statement caching for frequently used queries

**Issue: "Type errors with lastID"**
- Solution: Use compatibility wrapper or update to lastInsertRowid
- Check: Verify all RunResult.lastID references are updated

### Debugging Tips

1. **Enable verbose logging** during development:
   ```typescript
   const db = new Database(dbPath, { verbose: console.log });
   ```

2. **Test database operations in isolation** before full integration

3. **Use better-sqlite3's built-in error messages** - they're more descriptive than sqlite3

4. **Monitor WAL file growth** during development and testing

5. **Verify foreign key constraints** are working as expected

## Dependencies
- Phase 6 must be completed (all scripts migrated)

## Prerequisites
- All previous phases completed successfully
- Test environment prepared
- Backup of working system available

## Deliverables
- Confirmed app behavior and improved performance metrics.
- All functional tests passing
- Performance benchmarks documented
- Any issues identified and resolved
- System ready for optimization phase
