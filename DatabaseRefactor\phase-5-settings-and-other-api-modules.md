# Phase 5: Settings and Other API Modules

**AI Agent Instructions**: Use extended thinking for all decisions. Meticulously review this plan before execution. Always read and analyze files thoroughly before making any modifications. Follow this plan step-by-step without deviation.

## Overview
**Files Requiring Migration:**
- `electron/main/api/settings-api.ts` (HIGH PRIORITY - Complex transactions)
- `electron/main/api/recent-items-api.ts` (MEDIUM PRIORITY - Helper replacement)
- `electron/main/api/media-api.ts` (MEDIUM PRIORITY - Type dependency fix)
- `electron/main/api/books-api.ts` (LOW PRIORITY - Import verification)
- `electron/main/api/notes-api.ts` (LOW PRIORITY - Import verification)
- `electron/main/api/folders-api.ts` (LOW PRIORITY - Import verification)
- 10 sync-logic files (LOW PRIORITY - Import verification)

**Files EXCLUDED (Already Migrated):**
- `electron/main/api/timer-api.ts` ✅ (Already uses better-sqlite3)

**Complexity:** HIGH - Complex transaction logic, type dependencies, and extensive verification needed

## Migration Strategy

### Phase 5A: Critical Type Dependencies (FIRST)
**Priority**: CRITICAL - Must be done first to prevent build failures

### Phase 5B: Complex Transaction Logic (SECOND)
**Priority**: HIGH - Requires careful migration and testing

### Phase 5C: Helper Replacement (THIRD)
**Priority**: MEDIUM - Straightforward but needs validation

### Phase 5D: Import Verification (FOURTH)
**Priority**: LOW - Verification and compatibility testing

### Phase 5E: Integration Testing (FINAL)
**Priority**: CRITICAL - Comprehensive testing across all migrated modules

## Detailed Tasks

### 5A.1 media-api.ts - Fix Type Dependencies
**CRITICAL**: This file imports `RunResult` from sqlite3 directly, which will cause build failures.

**Current Issues:**
```typescript
// Line 6 - Direct sqlite3 import
import { RunResult } from 'sqlite3';
// Lines 102, 195, 223 - Uses RunResult type
const result: RunResult = await dbRun(query, [...]);
```

**Migration Steps:**
1. Remove sqlite3 import: `import { RunResult } from 'sqlite3';`
2. Update type usage to use better-sqlite3 return types
3. Replace `RunResult` with `{ changes: number; lastInsertRowid: number }`
4. Update all function signatures that reference RunResult
5. Test file upload, cover image handling, and media operations

### 5B.1 settings-api.ts - Complex Transaction Migration
**HIGH PRIORITY**: Contains complex manual transaction logic that needs careful conversion.

**Current Issues:**
```typescript
// Lines 225-274 - Manual transaction handling in setActiveTheme()
await new Promise<void>((resolve, reject) => {
    db.run('BEGIN TRANSACTION', (err) => (err ? reject(err) : resolve()));
});
// ... multiple manual transaction steps
await new Promise<void>((resolve, reject) => {
    db.run('COMMIT', (err) => (err ? reject(err) : resolve()));
});
```

**Migration Steps:**
1. Replace sqlite3 imports with database-api helpers
2. Convert manual transaction logic to `db.transaction()` pattern
3. Update `setActiveTheme()` function to use better-sqlite3 transactions
4. Replace callback-based helpers (dbGet, dbAll, dbRun) with database-api imports
5. Test theme switching and complex settings operations
6. Verify transaction rollback behavior

**Target Pattern:**
```typescript
const setActiveThemeTransaction = db.transaction((themeId: number) => {
    db.prepare('UPDATE theme_settings SET is_active = 0').run();
    const result = db.prepare('UPDATE theme_settings SET is_active = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?').run(themeId);
    if (result.changes === 0) {
        throw new Error(`Theme with ID ${themeId} not found.`);
    }
    return result;
});
```

### 5C.1 recent-items-api.ts - Helper Replacement
**MEDIUM PRIORITY**: Straightforward helper replacement.

**Current Issues:**
- Uses direct sqlite3 imports and callback-based helpers
- Lines 3, 48-90 contain callback-based database helpers

**Migration Steps:**
1. Remove sqlite3 imports: `import sqlite3 from 'sqlite3';`
2. Remove local helper functions (dbGet, dbAll, dbRun) - lines 48-90
3. Import helpers from database-api: `import { dbGet, dbAll, dbRun } from '../database/database-api';`
4. Update all function calls to use imported helpers
5. Test recent items tracking for notes and books

### 5D.1 Import Verification - Low Priority Files
**LOW PRIORITY**: These files use database-api.ts helpers and should work after Phase 3.

**Files to Verify:**
1. `books-api.ts` - Uses database-api.ts helpers (lines 2-19)
2. `notes-api.ts` - Uses database-api.ts helpers (lines 2-14)
3. `folders-api.ts` - Uses database-api.ts helpers (lines 2-18)

**Verification Steps:**
1. Confirm no direct sqlite3 imports remain
2. Verify all database operations use database-api.ts helpers
3. Test basic CRUD operations for each module
4. Check for any RunResult.lastID references that need updating

### 5D.2 Sync Logic Files Verification
**LOW PRIORITY**: 10 files in `electron/main/api/sync-logic/` directory.

**Files to Verify:**
- `auto-sync.ts`
- `change-detector.ts`
- `conflict-resolver.ts`
- `file-operations.ts`
- `import-handler.ts`
- `manifest-manager.ts` (uses dbAll, dbGet - lines 11-12)
- `media-utils.ts`
- `sync-api.ts` (uses dbGet, dbAll - line 12)
- `types.ts`
- `unified-sync-engine.ts`

**Verification Steps:**
1. Check each file for direct sqlite3 imports
2. Verify database operations use database-api.ts helpers
3. Test sync operations after migration
4. Verify manifest generation and sync functionality

### 5E.1 Integration Testing
**CRITICAL**: Comprehensive testing across all migrated modules.

**Test Categories:**
1. **Cross-API Operations**: Notes with media, books with folders
2. **Transaction Integrity**: Settings operations, complex workflows
3. **Sync Operations**: Full sync cycle testing
4. **Performance**: Verify better-sqlite3 performance improvements
5. **Error Handling**: Ensure proper error propagation

## Dependencies
- Phase 3 must be completed (database-api.ts migrated)
- Phase 4 must be completed (timer-api already migrated ✅)

## Prerequisites
- database-api.ts fully migrated to better-sqlite3
- timer-api.ts confirmed as already migrated
- Understanding of each API module's current database usage patterns
- Test environment prepared for comprehensive integration testing

## Risk Assessment

### HIGH RISK
- **Settings API transaction logic** - Complex manual transaction handling
- **Media API type dependencies** - Could cause build failures if not fixed first
- **Integration complexity** - Many interdependent API modules

### MEDIUM RISK
- **Sync logic compatibility** - Large number of files to verify
- **Performance regression** - Need to verify better-sqlite3 improvements

### LOW RISK
- **Import-only updates** - Straightforward replacements for most files

## Execution Checklist

### Phase 5A: Critical Type Dependencies ⚠️ MUST BE FIRST
- [ ] **5A.1** Migrate `media-api.ts` type dependencies
  - [ ] Remove `import { RunResult } from 'sqlite3';` (line 6)
  - [ ] Update `RunResult` type usage (lines 102, 195, 223)
  - [ ] Replace with `{ changes: number; lastInsertRowid: number }`
  - [ ] Test file upload and cover image operations
  - [ ] Verify no build errors

### Phase 5B: Complex Transaction Logic 🔄 HIGH PRIORITY
- [ ] **5B.1** Migrate `settings-api.ts` transaction logic
  - [ ] Remove sqlite3 imports (lines 3, 6)
  - [ ] Remove local helpers (lines 34-74)
  - [ ] Import from database-api: `import { dbGet, dbAll, dbRun } from '../database/database-api';`
  - [ ] Convert `setActiveTheme()` manual transactions (lines 225-274)
  - [ ] Test theme switching functionality
  - [ ] Verify transaction rollback behavior

### Phase 5C: Helper Replacement 🔧 MEDIUM PRIORITY
- [ ] **5C.1** Migrate `recent-items-api.ts` helpers
  - [ ] Remove sqlite3 imports (line 3)
  - [ ] Remove local helpers (lines 48-90)
  - [ ] Import from database-api
  - [ ] Test recent items tracking
  - [ ] Verify upsert logic works correctly

### Phase 5D: Import Verification 📋 LOW PRIORITY
- [ ] **5D.1** Verify API modules compatibility
  - [ ] Check `books-api.ts` - no direct sqlite3 usage ✅
  - [ ] Check `notes-api.ts` - no direct sqlite3 usage ✅
  - [ ] Check `folders-api.ts` - no direct sqlite3 usage ✅
  - [ ] Test basic CRUD operations for each
- [ ] **5D.2** Verify sync logic files
  - [ ] Check all 10 sync-logic files for sqlite3 imports
  - [ ] Verify `manifest-manager.ts` database operations
  - [ ] Verify `sync-api.ts` database operations
  - [ ] Test sync functionality

### Phase 5E: Integration Testing 🧪 CRITICAL
- [ ] **5E.1** Cross-module testing
  - [ ] Test notes with media attachments
  - [ ] Test books with folder operations
  - [ ] Test settings with sync operations
- [ ] **5E.2** Performance verification
  - [ ] Measure database operation speeds
  - [ ] Verify better-sqlite3 improvements
  - [ ] Document performance gains
- [ ] **5E.3** Error handling verification
  - [ ] Test transaction rollbacks
  - [ ] Test error propagation
  - [ ] Verify graceful failure handling

## Code Examples

### Media API Type Fix
```typescript
// BEFORE (lines 102, 195, 223)
const result: RunResult = await dbRun(query, [
  noteId, bookId, filePath, uniqueFileName, fileType, fileSize, isCover ? 1 : 0
]);

// AFTER
const result = await dbRun(query, [
  noteId, bookId, filePath, uniqueFileName, fileType, fileSize, isCover ? 1 : 0
]);
// result now has { changes: number; lastInsertRowid: number }

if (!result.lastInsertRowid) {
  throw new Error('Failed to create media file entry, no ID returned');
}
```

### Settings API Transaction Fix
```typescript
// BEFORE (lines 225-274) - Manual transaction
const db: Database = getDatabase();
try {
  await new Promise<void>((resolve, reject) => {
    db.run('BEGIN TRANSACTION', (err) => (err ? reject(err) : resolve()));
  });
  // ... multiple manual steps
  await new Promise<void>((resolve, reject) => {
    db.run('COMMIT', (err) => (err ? reject(err) : resolve()));
  });
} catch (error) {
  await new Promise<void>((resolve) => {
    db.run('ROLLBACK', () => resolve());
  });
}

// AFTER - better-sqlite3 transaction
import { withTransaction } from '../database/database-api';

const result = await withTransaction(() => {
  const db = getDatabase();

  // Set all themes to inactive
  db.prepare('UPDATE theme_settings SET is_active = 0').run();

  // Set the specified theme to active
  const updateResult = db.prepare(
    'UPDATE theme_settings SET is_active = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?'
  ).run(themeId);

  if (updateResult.changes === 0) {
    throw new Error(`Theme with ID ${themeId} not found.`);
  }

  return updateResult;
});
```

## Deliverables
- ✅ `media-api.ts` migrated with type dependencies fixed
- ✅ `settings-api.ts` migrated with complex transaction logic converted
- ✅ `recent-items-api.ts` migrated with helper replacement
- ✅ All remaining API modules verified for compatibility
- ✅ All 10 sync logic files verified for compatibility
- ✅ Comprehensive integration testing completed
- ✅ Performance improvements documented and verified
- ✅ No breaking changes to external API interfaces
- ✅ All build errors resolved and type safety maintained
